import { createId } from "@paralleldrive/cuid2";
/**
 * ! Executing this script will delete all data in your database and seed it with 10 trade.
 * ! Make sure to adjust the script to your needs.
 * Use any TypeScript runner to run this script, for example: `npx tsx seed.ts`
 * Learn more about the Seed Client by following our guide: https://docs.snaplet.dev/seed/getting-started
 */
import { createSeedClient } from "@snaplet/seed";

const main = async () => {
	const seed = await createSeedClient({
		models: {
			organization: {
				data: {
					id: createId,
				},
			},
			trade: {
				data: {
					id: createId,
				},
			},
			user: {
				data: {
					id: createId,
				},
			},
			job: {
				data: {
					id: createId,
				},
			},
			property: {
				data: {
					id: createId,
				},
			},
			bid: {
				data: {
					id: createId,
				},
			},
		},
	});

	// Truncate all tables in the database
	await seed.$resetDatabase();

	const { trade } = await seed.trade((x) => x(10));

	const { organization } = await seed.organization(
		(x) =>
			x(10, {
				memberships: [{ user: {} }],
			}),
		{ connect: { trade } },
	);

	const { property } = await seed.property((x) => x(10), {
		connect: { organization },
	});

	const { job } = await seed.job((x) => x(10), { connect: { property } });

	await seed.bid((x) => x(10), { connect: { job, organization } });

	// Type completion not working? You might want to reload your TypeScript Server to pick up the changes

	console.log("Database seeded successfully!");

	process.exit();
};

main();
