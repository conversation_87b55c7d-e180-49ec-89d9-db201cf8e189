"use client";

import type { Organization } from "@/db/generated";
import { TableCell, TableRow } from "../ui/table";

export default function TradeOrganizations({
  organizations,
}: {
  organizations: Organization[];
}) {
  return (
    <>
      {organizations?.map((org) => (
        <TableRow key={org.id}>
          <TableCell colSpan={3}>{org.name}</TableCell>
        </TableRow>
      ))}
    </>
  );
}
