import { CheckCircle2Icon } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function ProfessionalsPage() {
  return (
    <main className="bg-white">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div className="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
          <div className="text-center">
            <h1 className="font-bold text-4xl text-gray-900 tracking-tight sm:text-6xl">
              Grow Your Business with TradeCrews
            </h1>
            <p className="mt-6 text-gray-600 text-lg leading-8">
              Connect with homeowners looking for your expertise, manage
              projects efficiently, and build your reputation on the platform
              trusted by professionals.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/sign-up/professional">
                <Button variant="tc_orange" size="lg">
                  Join as a Professional
                </Button>
              </Link>
              <Link
                href="#benefits"
                className="font-semibold text-gray-900 text-sm leading-6"
              >
                Learn more <span aria-hidden="true">→</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div id="benefits" className="bg-gray-50 py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="font-semibold text-base text-tradecrews-blue leading-7">
              For Professionals
            </h2>
            <p className="mt-2 font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
              Everything you need to succeed
            </p>
            <p className="mt-6 text-gray-600 text-lg leading-8">
              TradeCrews provides the tools and opportunities you need to take
              your trade business to the next level.
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 font-semibold text-base text-gray-900 leading-7">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  Find New Clients
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base text-gray-600 leading-7">
                  <p className="flex-auto">
                    Connect with homeowners actively seeking your specific
                    skills and services in your local area.
                  </p>
                </dd>
              </div>
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 font-semibold text-base text-gray-900 leading-7">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  Build Your Reputation
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base text-gray-600 leading-7">
                  <p className="flex-auto">
                    Showcase your work, collect reviews, and build a stellar
                    reputation that helps you win more business.
                  </p>
                </dd>
              </div>
              <div className="flex flex-col">
                <dt className="flex items-center gap-x-3 font-semibold text-base text-gray-900 leading-7">
                  <CheckCircle2Icon
                    className="h-5 w-5 flex-none text-tradecrews-blue"
                    aria-hidden="true"
                  />
                  Streamlined Management
                </dt>
                <dd className="mt-4 flex flex-auto flex-col text-base text-gray-600 leading-7">
                  <p className="flex-auto">
                    Manage quotes, schedules, and client communications all in
                    one place with our easy-to-use tools.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-white py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="font-bold text-3xl text-gray-900 tracking-tight sm:text-4xl">
              Ready to grow your business?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-gray-600 text-lg leading-8">
              Join thousands of professionals who are expanding their client
              base and increasing their revenue with TradeCrews.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link href="/sign-up/professional">
                <Button variant="tc_orange" size="lg">
                  Sign up now
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
