"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  Briefcase,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Filter,
  Hammer,
  Search,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import type { Prisma } from "@/db/generated";
import { useOrganization } from "@/lib/contexts/organization-context";

export function ContractorDashboard() {
  const trpc = useTRPC();
  const { organization } = useOrganization();
  const [searchQuery, setSearchQuery] = useState("");
  const organizationId = organization?.id;

  const { data: allJobs, isLoading: isLoadingAllJobs } = useQuery(
    trpc.jobs.listPublished.queryOptions(undefined, {
      enabled: !!organizationId,
    })
  );

  const { data: myBids, isLoading: isLoadingMyBids } = useQuery(
    trpc.bids.listForOrganization.queryOptions(
      { organizationId: organizationId || "" },
      { enabled: !!organizationId }
    )
  );

  const { data: activeJobs, isLoading: isLoadingActiveJobs } = useQuery(
    trpc.jobs.listActiveForOrganization.queryOptions(
      { organizationId: organizationId || "" },
      { enabled: !!organizationId }
    )
  );

  // Filter jobs based on search query
  const filteredJobs = allJobs?.filter((job) =>
    job.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group jobs by status
  const jobsByCategory = {
    newest: filteredJobs || [],
    bids: myBids?.map((bid) => bid.job) || [],
    active: activeJobs || [],
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-semibold text-xl">
              <div className="flex items-center gap-2">
                <Building className="h-5 w-5 text-orange-500" />
                {organization?.name || "Organization"} Dashboard
              </div>
            </CardTitle>
            <CardDescription>
              Find new jobs and manage your current projects
            </CardDescription>
          </div>
        </div>
        <div className="mt-4 flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search jobs..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="newest" className="w-full">
          <div className="px-6">
            <TabsList className="h-auto w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger
                value="newest"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Newest Jobs
                <Badge className="ml-2 bg-blue-100 text-blue-800 hover:bg-blue-200">
                  {jobsByCategory.newest.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="bids"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Our Bids
                <Badge className="ml-2 bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                  {jobsByCategory.bids.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="active"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Active Jobs
                <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
                  {jobsByCategory.active.length}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="newest" className="m-0">
            <JobsList
              jobs={jobsByCategory.newest}
              isLoading={isLoadingAllJobs}
              icon={<Briefcase className="h-4 w-4 text-blue-500" />}
              actionLabel="Bid Now"
              actionHref={(id) => `/jobs/${id}/bid`}
            />
          </TabsContent>

          <TabsContent value="bids" className="m-0">
            <JobsList
              jobs={jobsByCategory.bids}
              isLoading={isLoadingMyBids}
              icon={<Hammer className="h-4 w-4 text-yellow-500" />}
              actionLabel="View Bid"
              actionHref={(id) => `/bids/${id}`}
            />
          </TabsContent>

          <TabsContent value="active" className="m-0">
            <JobsList
              jobs={jobsByCategory.active}
              isLoading={isLoadingActiveJobs}
              icon={<CheckCircle className="h-4 w-4 text-green-500" />}
              actionLabel="View Job"
              actionHref={(id) => `/jobs/${id}/details`}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t px-6 py-4">
        <div className="text-muted-foreground text-xs">
          Showing {filteredJobs?.length || 0} available jobs
        </div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/jobs/browse">Browse All Jobs</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

type Job = Prisma.JobGetPayload<{
  include: { property: { include: { organization: true } } };
}>;

interface JobsListProps {
  jobs: Job[];
  isLoading: boolean;
  icon: React.ReactNode;
  actionLabel: string;
  actionHref: (id: string) => string;
}

function JobsList({
  jobs,
  isLoading,
  icon,
  actionLabel,
  actionHref,
}: JobsListProps) {
  if (isLoading) {
    return (
      <div className="divide-y">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-4">
            <div className="space-y-3">
              <Skeleton className="h-5 w-1/3" />
              <div className="flex gap-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <div className="mb-2 text-muted-foreground">No jobs found</div>
      </div>
    );
  }

  return (
    <div className="divide-y">
      {jobs.map((job) => (
        <div key={job.id} className="p-4 transition-colors hover:bg-muted/50">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center gap-2">
                {icon}
                <Link
                  href={`/jobs/${job.id}`}
                  className="font-medium text-lg transition-colors hover:text-orange-600"
                >
                  {job.name}
                </Link>
                <Badge className="bg-blue-100 text-blue-800">
                  {job.property?.name || "Property"}
                </Badge>
              </div>
              <div className="mt-1 flex flex-wrap items-center gap-x-4 gap-y-1 text-muted-foreground text-sm">
                <div className="flex items-center gap-1">
                  <Building className="h-3.5 w-3.5" />
                  <span>
                    {job.property?.organization?.name || "Organization"}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3.5 w-3.5" />
                  <span>
                    Starts: {format(new Date(job.startsAt), "MMM d, yyyy")}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3.5 w-3.5" />
                  <span>
                    Deadline: {format(new Date(job.deadline), "MMM d, yyyy")}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <DollarSign className="h-3.5 w-3.5" />
                  <span>Budget: ${job.budget}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={actionHref(job.id)}>{actionLabel}</Link>
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
