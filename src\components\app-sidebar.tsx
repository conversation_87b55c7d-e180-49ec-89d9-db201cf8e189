"use client";

import { useAuth, useUser } from "@clerk/nextjs";
import { ChevronUp } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { OrgSwitcher } from "./org-switcher";
import logo from "./tc-logomark.webp";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "./ui/sidebar";

export function AppSidebar({
  menu,
}: Readonly<{
  menu: {
    title: string;
    href: string;
    icon: React.ElementType;
  }[];
}>) {
  const { user } = useUser();
  const { signOut } = useAuth();
  const isAdmin = user?.publicMetadata.admin || false;

  return (
    <Sidebar variant="inset" collapsible="offcanvas">
      {isAdmin ? (
        <SidebarHeader>
          <Image alt="TradeCrews" src={logo} width={40} />
        </SidebarHeader>
      ) : (
        <SidebarHeader>
          <OrgSwitcher />
        </SidebarHeader>
      )}
      <SidebarContent>
        <SidebarMenu>
          {menu.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild>
                <Link href={item.href}>
                  <item.icon width={24} />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton size={"lg"}>
                  <Avatar>
                    <AvatarImage src={user?.imageUrl} />
                    <AvatarFallback>TC</AvatarFallback>
                  </Avatar>
                  <span>{user?.fullName}</span>
                  <ChevronUp className="ml-auto" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="right"
                className="w-[var(--radix-popper-anchor-width)]"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild>
                  <Link href="/profile">Profile</Link>
                </DropdownMenuItem>
                {isAdmin && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin/dashboard">Admin Dashboard</Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <button
                    type="button"
                    className="w-full"
                    onClick={() => signOut()}
                  >
                    Sign Out
                  </button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
