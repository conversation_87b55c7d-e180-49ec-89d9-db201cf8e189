import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/db";
import { triggerPresenceEvent } from "@/lib/pusher-server";

const presenceSchema = z.object({
  chatId: z.string(),
  userId: z.string(),
  status: z.enum(["online", "offline"]),
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { chatId, userId: requestUserId, status } = presenceSchema.parse(body);

    // Verify the requesting user matches the authenticated user
    if (userId !== requestUserId) {
      return NextResponse.json({ error: "User mismatch" }, { status: 403 });
    }

    // Verify user has access to this chat
    const chat = await db.chat.findUnique({
      where: { id: chatId },
      include: {
        bid: {
          include: {
            job: {
              include: {
                property: {
                  include: {
                    organization: {
                      include: {
                        memberships: true,
                      },
                    },
                  },
                },
              },
            },
            organization: {
              include: {
                memberships: true,
              },
            },
          },
        },
      },
    });

    if (!chat) {
      return NextResponse.json({ error: "Chat not found" }, { status: 404 });
    }

    // Check if user is a member of either organization
    const propertyOwnerMemberships = chat.bid.job.property.organization.memberships;
    const bidderMemberships = chat.bid.organization.memberships;

    const isPropertyOwner = propertyOwnerMemberships.some(
      (membership) => membership.userId === userId
    );
    const isBidder = bidderMemberships.some(
      (membership) => membership.userId === userId
    );

    if (!isPropertyOwner && !isBidder) {
      return NextResponse.json(
        { error: "Access denied to this chat" },
        { status: 403 }
      );
    }

    // Trigger presence event
    await triggerPresenceEvent(chatId, userId, status);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Presence update error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
