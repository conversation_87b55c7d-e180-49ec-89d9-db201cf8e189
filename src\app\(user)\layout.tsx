"use client";

import { ClipboardListIcon, LayoutDashboardIcon, MapIcon } from "lucide-react";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { OrganizationProvider } from "@/lib/contexts/organization-context";

export default function UserLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const menu = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboardIcon,
    },
    {
      title: "Properties",
      href: "/properties",
      icon: MapIcon,
    },
    {
      title: "My Jobs",
      href: "/jobs",
      icon: ClipboardListIcon,
    },
  ];

  return (
    <SidebarProvider>
      <OrganizationProvider>
        <AppSidebar menu={menu} />
        <SidebarInset>{children}</SidebarInset>
      </OrganizationProvider>
    </SidebarProvider>
  );
}
