import { SiX } from "@icons-pack/react-simple-icons";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";

import dieter from "./dieter.jpg";
import paul from "./paul.jpg";

const people = [
  {
    name: "<PERSON><PERSON>",
    role: "Fullstack Developer",
    imageUrl: dieter,
    xUrl: "https://x.com/coder2000",
    linkedinUrl: "https://linkedin.com/in/dieter.lunn",
    featured: true,
  },
  {
    name: "<PERSON>",
    role: "Founder",
    imageUrl: paul,
    xUrl: "https://x.com/paulmillefolie",
    linkedinUrl: "https://www.linkedin.com/in/paul-millefolie/",
    featured: true,
  },
];

export default function People() {
  // Group members by featured status
  const featuredMembers = people.filter((person) => person.featured);
  const regularMembers = people.filter((person) => !person.featured);

  return (
    <section className="relative overflow-hidden bg-white py-24 sm:py-32">
      {/* Background decorative elements */}
      <div className="pointer-events-none absolute top-0 left-0 h-full w-full overflow-hidden">
        <div className="-top-[30%] -left-[10%] absolute h-[70%] w-[50%] rounded-full bg-orange-500/5 blur-3xl" />
        <div className="-bottom-[30%] -right-[10%] absolute h-[70%] w-[50%] rounded-full bg-orange-500/5 blur-3xl" />
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto mb-16 max-w-2xl text-center lg:mx-0">
          <Badge
            variant="outline"
            className="mb-4 border-orange-500/20 bg-orange-500/10 text-orange-500 hover:bg-orange-500/20"
          >
            Our Team
          </Badge>
          <h2 className="text-pretty font-semibold text-4xl text-gray-900 tracking-tight sm:text-5xl">
            The people behind TradeCrews
          </h2>
          <p className="mt-6 text-gray-600 text-lg/8">
            We're a dynamic group of individuals who are passionate about what
            we do and dedicated to delivering the best results for our clients.
          </p>
        </div>

        {/* Featured team members */}
        {featuredMembers.length > 0 && (
          <div className="mb-16">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {featuredMembers.map((person) => (
                <div
                  key={person.name}
                  className="group relative overflow-hidden rounded-xl border border-orange-500/20 bg-gradient-to-br from-orange-500/10 to-orange-600/5 transition-all duration-300 hover:border-orange-500/40 hover:shadow-[0_0_30px_rgba(249,115,22,0.1)]"
                >
                  <div className="flex flex-col gap-6 p-6 md:flex-row">
                    <div className="aspect-square w-full overflow-hidden rounded-lg md:w-1/3">
                      <Image
                        src={person.imageUrl}
                        alt={person.name}
                        className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
                      />
                    </div>
                    <div className="w-full md:w-2/3">
                      <h3 className="font-bold text-gray-900 text-xl">
                        {person.name}
                      </h3>
                      <p className="mt-1 font-medium text-orange-500 text-sm">
                        {person.role}
                      </p>

                      <p className="mt-4 text-gray-600">
                        Passionate about creating innovative solutions that
                        connect homeowners with skilled professionals, making
                        home improvement projects seamless and stress-free.
                      </p>

                      <div className="mt-6 flex gap-3">
                        {person.xUrl && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="rounded-full border-orange-500/20 text-gray-400 hover:bg-orange-500/10 hover:text-orange-500"
                            asChild
                          >
                            <a
                              href={person.xUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <SiX className="mr-2 h-4 w-4" />X
                            </a>
                          </Button>
                        )}
                        {person.linkedinUrl && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="rounded-full border-orange-500/20 text-gray-400 hover:bg-orange-500/10 hover:text-orange-500"
                            asChild
                          >
                            <a
                              href={person.linkedinUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Linkedin className="mr-2 h-4 w-4" />
                              LinkedIn
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Regular team members */}
        {regularMembers.length > 0 && (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 sm:gap-8 md:grid-cols-3 lg:grid-cols-4">
            {regularMembers.map((person) => (
              <div
                key={person.name}
                className="group relative overflow-hidden rounded-xl border border-gray-200 bg-background transition-all duration-300 hover:border-orange-500/30 hover:shadow-[0_0_30px_rgba(249,115,22,0.07)]"
              >
                <div className="aspect-[3/4] overflow-hidden">
                  <Image
                    src={person.imageUrl}
                    alt={person.name}
                    className="h-full w-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
                  />
                </div>
                <div className="p-5">
                  <h3 className="font-semibold text-gray-900 text-lg">
                    {person.name}
                  </h3>
                  <p className="mt-1 text-gray-600 text-sm">{person.role}</p>

                  <div className="mt-4 flex gap-2">
                    {person.xUrl && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-full text-gray-500 hover:bg-orange-500/10 hover:text-orange-500"
                        asChild
                      >
                        <a
                          href={person.xUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <SiX className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                    {person.linkedinUrl && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-full text-gray-500 hover:bg-orange-500/10 hover:text-orange-500"
                        asChild
                      >
                        <a
                          href={person.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Linkedin className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Join the team CTA */}
        <div className="mt-20 text-center">
          <div className="inline-block rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 p-0.5">
            <Button
              variant="outline"
              className="group rounded-[6px] border-none bg-white hover:bg-gray-50"
            >
              Join Our Team
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
