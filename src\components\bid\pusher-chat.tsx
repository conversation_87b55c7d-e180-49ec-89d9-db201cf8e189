"use client";

import { useUser } from "@clerk/nextjs";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState, useCallback, useRef } from "react";
import { format } from "date-fns";
import { Send, Users } from "lucide-react";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { 
  subscribeToChatChannel, 
  unsubscribeFromChatChannel,
  sendTypingIndicator,
  updatePresence 
} from "@/lib/pusher-client";

interface Message {
  id: string;
  content: string;
  senderId: string;
  senderType: string;
  createdAt: Date;
  chatId: string;
}

export function PusherChat({
  bidId,
  userId,
}: {
  bidId: string;
  userId: string;
}) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { user } = useUser();
  const [newMessage, setNewMessage] = useState("");
  const [chatId, setChatId] = useState<string>();
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch messages
  const { data: messages, isLoading } = useQuery(
    trpc.messages.listForBid.queryOptions({ bidId })
  );

  // Create message mutation
  const createMessageMutation = useMutation({
    mutationFn: trpc.messages.create.mutate,
    onSuccess: () => {
      setNewMessage("");
      // Messages will be updated via Pusher event
    },
  });

  // Handle new messages from Pusher
  const handlePusherMessage = useCallback((data: Message) => {
    // Invalidate and refetch messages to update the local cache
    queryClient.invalidateQueries({
      queryKey: trpc.messages.listForBid.getQueryKey({ bidId })
    });
  }, [queryClient, trpc.messages.listForBid, bidId]);

  // Handle typing indicators from Pusher
  const handlePusherTyping = useCallback((data: { userId: string; isTyping: boolean }) => {
    if (data.userId === userId) return; // Ignore own typing

    setTypingUsers(prev => {
      const newSet = new Set(prev);
      if (data.isTyping) {
        newSet.add(data.userId);
      } else {
        newSet.delete(data.userId);
      }
      return newSet;
    });
  }, [userId]);

  // Handle presence updates from Pusher
  const handlePusherPresence = useCallback((data: { userId: string; status: string }) => {
    if (data.userId === userId) return; // Ignore own presence

    setOnlineUsers(prev => {
      const newSet = new Set(prev);
      if (data.status === "online") {
        newSet.add(data.userId);
      } else {
        newSet.delete(data.userId);
      }
      return newSet;
    });
  }, [userId]);

  // Extract chatId from messages
  useEffect(() => {
    if (messages && messages.length > 0) {
      setChatId(messages[0]?.chatId);
    }
  }, [messages]);

  // Set up Pusher subscription when chatId is available
  useEffect(() => {
    if (!chatId) return;

    const channel = subscribeToChatChannel(chatId, {
      onMessage: handlePusherMessage,
      onTyping: handlePusherTyping,
      onPresence: handlePusherPresence,
      onSubscriptionSucceeded: () => {
        console.log("Successfully subscribed to chat channel");
        // Update presence to online
        updatePresence(chatId, userId, "online");
      },
      onSubscriptionError: (error) => {
        console.error("Failed to subscribe to chat channel:", error);
      },
    });

    // Update presence to online when component mounts
    updatePresence(chatId, userId, "online");

    return () => {
      // Update presence to offline when component unmounts
      updatePresence(chatId, userId, "offline");
      unsubscribeFromChatChannel(chatId);
    };
  }, [chatId, handlePusherMessage, handlePusherTyping, handlePusherPresence, userId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Handle typing in message input
  const handleTyping = useCallback(() => {
    if (!chatId) return;

    // Send typing indicator
    sendTypingIndicator(chatId, userId, true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(chatId, userId, false);
    }, 3000);
  }, [chatId, userId]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    try {
      await createMessageMutation.mutateAsync({
        bidId,
        content: newMessage.trim(),
      });
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const getUserInitials = (senderId: string) => {
    // You might want to fetch actual user data here
    return senderId.slice(0, 2).toUpperCase();
  };

  const isOwnMessage = (senderId: string) => senderId === userId;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-muted-foreground">Loading messages...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-96 border rounded-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-muted/50">
        <h3 className="font-medium">Chat</h3>
        {onlineUsers.size > 0 && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            {onlineUsers.size + 1} online
          </Badge>
        )}
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages?.map((message) => (
            <div
              key={message.id}
              className={`flex ${isOwnMessage(message.senderId) ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`flex max-w-[70%] ${
                  isOwnMessage(message.senderId) ? "flex-row-reverse" : "flex-row"
                } items-start gap-2`}
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="" />
                  <AvatarFallback className="text-xs">
                    {getUserInitials(message.senderId)}
                  </AvatarFallback>
                </Avatar>
                <div
                  className={`rounded-lg px-3 py-2 ${
                    isOwnMessage(message.senderId)
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p
                    className={`text-xs mt-1 ${
                      isOwnMessage(message.senderId)
                        ? "text-primary-foreground/70"
                        : "text-muted-foreground"
                    }`}
                  >
                    {format(new Date(message.createdAt), "HH:mm")}
                  </p>
                </div>
              </div>
            </div>
          ))}
          
          {/* Typing indicator */}
          {typingUsers.size > 0 && (
            <div className="flex justify-start">
              <div className="flex items-center gap-2 text-muted-foreground text-sm">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                </div>
                <span>
                  {Array.from(typingUsers).join(", ")} {typingUsers.size === 1 ? "is" : "are"} typing...
                </span>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message input */}
      <form onSubmit={handleSendMessage} className="p-4 border-t">
        <div className="flex gap-2">
          <Input
            value={newMessage}
            onChange={(e) => {
              setNewMessage(e.target.value);
              handleTyping();
            }}
            placeholder="Type a message..."
            disabled={createMessageMutation.isPending}
          />
          <Button 
            type="submit" 
            size="icon"
            disabled={!newMessage.trim() || createMessageMutation.isPending}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </form>
    </div>
  );
}
