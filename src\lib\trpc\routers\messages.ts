import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "@/db";
import { triggerChatEvent } from "@/lib/pusher-server";
import { protectedProcedure, router } from "../trpc";

export const messagesRouter = router({
  listForBid: protectedProcedure
    .input(z.object({ bidId: z.string() }))
    .query(async ({ input, ctx }) => {
      const bid = await db.bid.findUnique({
        where: { id: input.bidId },
        include: {
          job: {
            include: {
              property: true,
            },
          },
          organization: true,
        },
      });

      if (!bid) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Bid not found",
        });
      }

      // Check if user has permission to view messages
      // Either they own the property or they submitted the bid
      const isPropertyOwner = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organizationId: bid.job.property.organizationId,
        },
      });

      const isBidder = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organizationId: bid.organizationId,
        },
      });

      if (!isPropertyOwner && !isBidder) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You don't have permission to view these messages",
        });
      }

      // Find or create a chat for this bid
      let chat = await db.chat.findUnique({
        where: { bidId: input.bidId },
      });

      if (!chat) {
        chat = await db.chat.create({
          data: { bidId: input.bidId },
        });
      }

      // Fetch messages
      const messages = await db.message.findMany({
        where: { chatId: chat.id },
        orderBy: { createdAt: "asc" },
      });

      return messages;
    }),

  create: protectedProcedure
    .input(
      z.object({
        bidId: z.string(),
        content: z.string().min(1),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const bid = await db.bid.findUnique({
        where: { id: input.bidId },
        include: {
          job: {
            include: {
              property: true,
            },
          },
          organization: true,
        },
      });

      if (!bid) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Bid not found",
        });
      }

      // Check if user has permission to send messages
      const isPropertyOwner = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organizationId: bid.job.property.organizationId,
        },
      });

      const isBidder = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organizationId: bid.organizationId,
        },
      });

      if (!isPropertyOwner && !isBidder) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You don't have permission to send messages",
        });
      }

      // Find or create a chat for this bid
      let chat = await db.chat.findUnique({
        where: { bidId: input.bidId },
      });

      if (!chat) {
        chat = await db.chat.create({
          data: { bidId: input.bidId },
        });
      }

      // Determine sender type
      const senderType = isPropertyOwner ? "homeowner" : "professional";

      // Create message
      const message = await db.message.create({
        data: {
          chatId: chat.id,
          content: input.content,
          senderId: ctx.auth.userId,
          senderType,
        },
      });

      // Trigger Pusher event for real-time message delivery
      try {
        await triggerChatEvent(chat.id, "message", {
          id: message.id,
          content: message.content,
          senderId: message.senderId,
          senderType: message.senderType,
          createdAt: message.createdAt,
          chatId: message.chatId,
        });
      } catch (error) {
        console.error("Failed to trigger Pusher event:", error);
        // Don't fail the message creation if Pusher fails
      }

      return message;
    }),
});
