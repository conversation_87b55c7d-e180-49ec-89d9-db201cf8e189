import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import Link from "next/link";
import { Header } from "@/components/header";
import { JobTable } from "@/components/job/job-table";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { buttonVariants } from "@/components/ui/button";

export default function JobsPage() {
  const queryClient = getQueryClient();
  void queryClient.prefetchQuery(trpc.jobs.listForUser.queryOptions());
  

  return (
    <>
      <Header title="Jobs">
        <Link
          href="/jobs/new"
          className={buttonVariants({ variant: "default" })}
        >
          Add Job
        </Link>
      </Header>
      <div className="p-8">
        <HydrationBoundary state={dehydrate(queryClient)}>
          <JobTable />
        </HydrationBoundary>
      </div>
    </>
  );
}
