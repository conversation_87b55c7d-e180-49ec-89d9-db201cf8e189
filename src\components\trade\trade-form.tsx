"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { type SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useTRPC } from "../trpc/client";

interface Trade {
  id: string;
  name: string;
}

interface TradeFormProps {
  initialData?: Trade | null | undefined;
}

const tradesSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

type FormSchema = z.infer<typeof tradesSchema>;

export function TradeForm({ initialData }: TradeFormProps) {
  const router = useRouter();
  const form = useForm<FormSchema>({
    resolver: zodResolver(tradesSchema),
    defaultValues: {
      name: "",
    },
    values: {
      name: initialData?.name || "",
    },
  });

  const queryClient = useQueryClient();
  const trpc = useTRPC();
  const listKey = trpc.trades.list.queryKey();

  const createTrade = useMutation(
    trpc.trades.create.mutationOptions({
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: listKey });
      },
    })
  );
  const updateTrade = useMutation(
    trpc.trades.update.mutationOptions({
      onSuccess: (data, variables) => {
        queryClient.setQueryData([listKey, { id: variables.id }], data);
      },
    })
  );

  const mutation = initialData ? updateTrade : createTrade;

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();

    const name = data.name;

    mutation.mutate(
      { name, id: initialData?.id || "" },
      {
        onSuccess: () => {
          router.push("/admin/trades");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating trade:", error);
        },
      }
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="max-w-md space-y-8"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Plumbing" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button type="submit">
            {initialData ? "Update" : "Create"} Trade
          </Button>
          <Button
            type="button"
            onClick={() => router.back()}
            variant="secondary"
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
