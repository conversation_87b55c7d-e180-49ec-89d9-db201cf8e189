"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  BarChart3,
  Calendar,
  Clock,
  DollarSign,
  Filter,
  PlusCircle,
  Search,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import type { Prisma } from "@/db/generated";
import { cn } from "@/lib/utils";

type JobStatus = "DRAFT" | "PUBLISHED" | "CLOSED" | "CANCELED" | "AWARDED";

const statusColors: Record<JobStatus, string> = {
  DRAFT: "bg-gray-100 text-gray-800",
  PUBLISHED: "bg-blue-100 text-blue-800",
  CLOSED: "bg-purple-100 text-purple-800",
  CANCELED: "bg-red-100 text-red-800",
  AWARDED: "bg-green-100 text-green-800",
};

export function JobsModule() {
  const trpc = useTRPC();
  const [searchQuery, setSearchQuery] = useState("");
  const { data: jobs, isLoading } = useQuery(
    trpc.jobs.listForUser.queryOptions()
  );

  // Filter jobs based on search query
  const filteredJobs = jobs?.filter((job) =>
    job.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group jobs by status
  const jobsByStatus = {
    all: filteredJobs || [],
    draft: filteredJobs?.filter((job) => job.status === "DRAFT") || [],
    published: filteredJobs?.filter((job) => job.status === "PUBLISHED") || [],
    closed: filteredJobs?.filter((job) => job.status === "CLOSED") || [],
    awarded: filteredJobs?.filter((job) => job.status === "AWARDED") || [],
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="font-semibold text-xl">My Jobs</CardTitle>
            <CardDescription>
              Manage and track all your property jobs
            </CardDescription>
          </div>
          <Button asChild>
            <Link href="/jobs/new" className="flex items-center gap-1">
              <PlusCircle className="h-4 w-4" />
              <span>New Project</span>
            </Link>
          </Button>
        </div>
        <div className="mt-4 flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute top-2.5 left-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search jobs..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="all" className="w-full">
          <div className="px-6">
            <TabsList className="h-auto w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger
                value="all"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                All Jobs
                <Badge className="ml-2 bg-gray-100 text-gray-800 hover:bg-gray-200">
                  {jobsByStatus.all.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="draft"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Drafts
                <Badge className="ml-2 bg-gray-100 text-gray-800 hover:bg-gray-200">
                  {jobsByStatus.draft.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="published"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Published
                <Badge className="ml-2 bg-blue-100 text-blue-800 hover:bg-blue-200">
                  {jobsByStatus.published.length}
                </Badge>
              </TabsTrigger>
              <TabsTrigger
                value="awarded"
                className="rounded-none px-4 py-3 data-[state=active]:border-orange-500 data-[state=active]:border-b-2 data-[state=active]:text-orange-600 data-[state=active]:shadow-none"
              >
                Awarded
                <Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
                  {jobsByStatus.awarded.length}
                </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="m-0">
            <JobsList jobs={jobsByStatus.all} isLoading={isLoading} />
          </TabsContent>
          <TabsContent value="draft" className="m-0">
            <JobsList jobs={jobsByStatus.draft} isLoading={isLoading} />
          </TabsContent>
          <TabsContent value="published" className="m-0">
            <JobsList jobs={jobsByStatus.published} isLoading={isLoading} />
          </TabsContent>
          <TabsContent value="awarded" className="m-0">
            <JobsList jobs={jobsByStatus.awarded} isLoading={isLoading} />
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t px-6 py-4">
        <div className="text-muted-foreground text-xs">
          Showing {filteredJobs?.length || 0} of {jobs?.length || 0} jobs
        </div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/jobs">View All Jobs</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

interface JobsListProps {
  jobs: Prisma.JobGetPayload<{ include: { property: true } }>[];
  isLoading: boolean;
}

function JobsList({ jobs, isLoading }: JobsListProps) {
  if (isLoading) {
    return (
      <div className="divide-y">
        {[1, 2, 3].map((i) => (
          <div key={i} className="p-4">
            <div className="space-y-3">
              <Skeleton className="h-5 w-1/3" />
              <div className="flex gap-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center px-4 py-12 text-center">
        <BarChart3 className="mb-4 h-12 w-12 text-muted-foreground" />
        <h3 className="font-medium text-lg">No jobs found</h3>
        <p className="mt-2 mb-6 max-w-md text-muted-foreground">
          You don't have any jobs in this category yet. Create a new job to get
          started.
        </p>
        <Button asChild>
          <Link href="/jobs/new">Create New Job</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="divide-y">
      {jobs.map((job) => (
        <div key={job.id} className="p-4 transition-colors hover:bg-muted/50">
          <div className="flex items-start justify-between">
            <div>
              <div className="flex items-center gap-2">
                <Link
                  href={`/jobs/${job.id}`}
                  className="font-medium text-lg transition-colors hover:text-orange-600"
                >
                  {job.name}
                </Link>
                <Badge
                  className={cn(
                    "text-xs",
                    statusColors[job.status as JobStatus]
                  )}
                >
                  {job.status}
                </Badge>
              </div>
              <div className="mt-1 flex flex-wrap items-center gap-x-4 gap-y-1 text-muted-foreground text-sm">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3.5 w-3.5" />
                  <span>
                    Starts: {format(new Date(job.startsAt), "MMM d, yyyy")}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3.5 w-3.5" />
                  <span>
                    Deadline: {format(new Date(job.deadline), "MMM d, yyyy")}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <DollarSign className="h-3.5 w-3.5" />
                  <span>Budget: ${job.budget}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/jobs/${job.id}/edit`}>Edit</Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/jobs/${job.id}`}>View</Link>
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
