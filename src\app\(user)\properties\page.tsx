"use client";

import { useQuery } from "@tanstack/react-query";
import { PlusIcon } from "lucide-react";
import { Head<PERSON> } from "@/components/header";
import { NewProperty } from "@/components/properties/new-property";
import { PropertyCard } from "@/components/properties/property-card";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import { useOrganization } from "@/lib/contexts/organization-context";

export default function PropertiesPage() {
  const { organization } = useOrganization();
  const trpc = useTRPC();
  const { data: properties, isLoading } = useQuery(
    trpc.properties.list.queryOptions({
      organizationId: organization?.id || "",
    })
  );

  return (
    <>
      <Header title="Properties" />

      <div className="container mx-auto p-6 md:p-8">
        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="h-[320px] animate-pulse rounded-lg bg-muted"
              />
            ))}
          </div>
        ) : properties?.length === 0 ? (
          <div className="flex flex-col items-center justify-center rounded-lg border border-muted-foreground/20 border-dashed bg-muted/10 p-12 text-center">
            <div className="rounded-full bg-orange-100 p-3">
              <PlusIcon className="h-6 w-6 text-orange-600" />
            </div>
            <h3 className="mt-4 font-medium text-lg">No properties yet</h3>
            <p className="mt-2 max-w-md text-muted-foreground text-sm">
              Get started by adding your first property. Properties help you
              organize your projects and manage your work.
            </p>
            <Button
              className="mt-6"
              onClick={() =>
                document.getElementById("new-property-trigger")?.click()
              }
            >
              Add Your First Property
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {properties?.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
            <div className="flex h-full items-center justify-center">
              <NewProperty />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
