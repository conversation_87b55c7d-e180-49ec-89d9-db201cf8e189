"use client";

import { useQuery } from "@tanstack/react-query";
import {
  createContext,
  Suspense,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTRPC } from "@/components/trpc/client";
import type { Organization } from "@/db/generated";

export type OrganizationContextType = {
  organization: Organization | null | undefined;
  setOrganization: (organization: Organization) => void;
  organizationList: Organization[] | null | undefined;
  setOrganizationList: (
    organizationList: Organization[] | null | undefined
  ) => void;
};

const OrganizationContext = createContext<OrganizationContextType>({
  organization: null,
  setOrganization: () => {},
  organizationList: null,
  setOrganizationList: () => {},
});

function OrganizationLoader({
  setOrganization,
  setOrganizationList,
}: {
  setOrganization: (org: Organization) => void;
  setOrganizationList: (orgList: Organization[]) => void;
}) {
  const trpc = useTRPC();
  const { data: defaultOrganization } = useQuery(
    trpc.organizations.firstForUser.queryOptions()
  );
  const { data: organizations } = useQuery(
    trpc.organizations.listForUser.queryOptions()
  );

  useEffect(() => {
    if (organizations) {
      setOrganizationList(organizations);
    }
  }, [organizations, setOrganizationList]);

  useEffect(() => {
    if (defaultOrganization) {
      setOrganization(defaultOrganization);
    }
  }, [defaultOrganization, setOrganization]);

  return null;
}

export function OrganizationProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [organization, setOrganization] = useState<
    Organization | null | undefined
  >(null);
  const [organizationList, setOrganizationList] = useState<
    Organization[] | null | undefined
  >(null);

  const value = {
    organization,
    setOrganization,
    organizationList,
    setOrganizationList,
  };

  return (
    <OrganizationContext.Provider value={value}>
      <Suspense fallback={null}>
        <OrganizationLoader
          setOrganization={setOrganization}
          setOrganizationList={setOrganizationList}
        />
      </Suspense>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  return useContext(OrganizationContext);
}
