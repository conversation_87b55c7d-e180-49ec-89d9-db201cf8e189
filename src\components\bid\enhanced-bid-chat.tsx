"use client";

import { useUser } from "@clerk/nextjs";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState, useCallback, useRef } from "react";
import type { Channel as ChatChannel } from "stream-chat";
import {
  Channel,
  Chat,
  MessageInput,
  MessageList,
  useCreateChatClient,
  Window,
} from "stream-chat-react";
import { useTRPC } from "@/components/trpc/client";
import { env } from "@/env";
import { 
  subscribeToChatChannel, 
  unsubscribeFromChatChannel,
  sendTypingIndicator,
  updatePresence 
} from "@/lib/pusher-client";

import "stream-chat-react/dist/css/v2/index.css";
import "@/styles/stream-chat.css";

interface Message {
  id: string;
  content: string;
  senderId: string;
  senderType: string;
  createdAt: Date;
  chatId: string;
}

export function EnhancedBidChat({
  bidId,
  userId,
  userToken,
}: {
  bidId: string;
  userId: string;
  userToken: string;
}) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { user } = useUser();
  const [channel, setChannel] = useState<ChatChannel>();
  const [chatId, setChatId] = useState<string>();
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Fetch bid details to get organization IDs
  const { data: bid } = useQuery(trpc.bids.one.queryOptions({ id: bidId }));

  // Fetch members from both organizations
  const { data: bidderMembers } = useQuery(
    trpc.organizations.getMembers.queryOptions(
      { organizationId: bid?.organizationId || "" },
      { enabled: !!bid?.organizationId }
    )
  );

  const { data: propertyOwnerMembers } = useQuery(
    trpc.organizations.getMembers.queryOptions(
      { organizationId: bid?.job?.property?.organizationId || "" },
      { enabled: !!bid?.job?.property?.organizationId }
    )
  );

  // Fetch messages to get chatId
  const { data: messages } = useQuery(
    trpc.messages.listForBid.queryOptions({ bidId })
  );

  const client = useCreateChatClient({
    apiKey: env.NEXT_PUBLIC_STREAM_API_KEY,
    tokenOrProvider: userToken,
    userData: {
      id: userId,
      name: user?.fullName || userId,
      image: user?.imageUrl,
    },
  });

  // Handle new messages from Pusher
  const handlePusherMessage = useCallback((data: Message) => {
    // Invalidate and refetch messages to update the local cache
    queryClient.invalidateQueries({
      queryKey: trpc.messages.listForBid.getQueryKey({ bidId })
    });

    // Update Stream Chat channel if it exists
    if (channel && data.senderId !== userId) {
      // Add message to Stream Chat (this will update the UI)
      channel.sendMessage({
        text: data.content,
        user: {
          id: data.senderId,
          name: data.senderId, // You might want to fetch actual user name
        },
      });
    }
  }, [queryClient, trpc.messages.listForBid, bidId, channel, userId]);

  // Handle typing indicators from Pusher
  const handlePusherTyping = useCallback((data: { userId: string; isTyping: boolean }) => {
    if (data.userId === userId) return; // Ignore own typing

    setTypingUsers(prev => {
      const newSet = new Set(prev);
      if (data.isTyping) {
        newSet.add(data.userId);
      } else {
        newSet.delete(data.userId);
      }
      return newSet;
    });
  }, [userId]);

  // Handle presence updates from Pusher
  const handlePusherPresence = useCallback((data: { userId: string; status: string }) => {
    if (data.userId === userId) return; // Ignore own presence

    setOnlineUsers(prev => {
      const newSet = new Set(prev);
      if (data.status === "online") {
        newSet.add(data.userId);
      } else {
        newSet.delete(data.userId);
      }
      return newSet;
    });
  }, [userId]);

  // Set up Pusher subscription when chatId is available
  useEffect(() => {
    if (!chatId) return;

    const channel = subscribeToChatChannel(chatId, {
      onMessage: handlePusherMessage,
      onTyping: handlePusherTyping,
      onPresence: handlePusherPresence,
      onSubscriptionSucceeded: () => {
        console.log("Successfully subscribed to chat channel");
        // Update presence to online
        updatePresence(chatId, userId, "online");
      },
      onSubscriptionError: (error) => {
        console.error("Failed to subscribe to chat channel:", error);
      },
    });

    // Update presence to online when component mounts
    updatePresence(chatId, userId, "online");

    return () => {
      // Update presence to offline when component unmounts
      updatePresence(chatId, userId, "offline");
      unsubscribeFromChatChannel(chatId);
    };
  }, [chatId, handlePusherMessage, handlePusherTyping, handlePusherPresence, userId]);

  // Extract chatId from messages
  useEffect(() => {
    if (messages && messages.length > 0) {
      setChatId(messages[0]?.chatId);
    }
  }, [messages]);

  // Set up Stream Chat channel
  useEffect(() => {
    if (!client || !bidderMembers || !propertyOwnerMembers || !user) return;

    const setupChannel = async () => {
      try {
        // Connect user with full profile information
        await client.connectUser(
          {
            id: userId,
            name: user.fullName || userId,
            image: user.imageUrl,
          },
          userToken
        );

        // First try to get the existing channel
        const existingChannels = await client.queryChannels({
          id: bidId,
          type: "messaging",
        });

        let channelInstance: ChatChannel;

        if (existingChannels.length > 0) {
          // Channel exists, use the first one
          channelInstance = existingChannels[0] as ChatChannel;
        } else {
          // Create a new channel for this bid
          channelInstance = client.channel("messaging", bidId, {
            members: [
              ...bidderMembers.map((member) => member.userId),
              ...propertyOwnerMembers.map((member) => member.userId),
            ],
          });
        }

        // Initialize the channel
        await channelInstance.watch();
        setChannel(channelInstance);
      } catch (error) {
        console.error("Error setting up chat channel:", error);
      }
    };

    setupChannel();
  }, [
    client,
    bidId,
    bidderMembers,
    propertyOwnerMembers,
    user,
    userToken,
    userId,
  ]);

  // Handle typing in message input
  const handleTyping = useCallback(() => {
    if (!chatId) return;

    // Send typing indicator
    sendTypingIndicator(chatId, userId, true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      sendTypingIndicator(chatId, userId, false);
    }, 3000);
  }, [chatId, userId]);

  if (!client || !channel) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        Loading chat...
      </div>
    );
  }

  return (
    <div className="relative">
      <Chat client={client} theme="stream-chat-custom-theme">
        <Channel channel={channel}>
          <Window>
            <MessageList />
            <div className="relative">
              {typingUsers.size > 0 && (
                <div className="px-4 py-2 text-sm text-muted-foreground">
                  {Array.from(typingUsers).join(", ")} {typingUsers.size === 1 ? "is" : "are"} typing...
                </div>
              )}
              <MessageInput 
                onKeyDown={handleTyping}
                additionalTextareaProps={{
                  onInput: handleTyping,
                }}
              />
            </div>
          </Window>
        </Channel>
      </Chat>
      
      {/* Online users indicator */}
      {onlineUsers.size > 0 && (
        <div className="absolute top-2 right-2 flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-xs text-muted-foreground">
            {onlineUsers.size} online
          </span>
        </div>
      )}
    </div>
  );
}
