"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { type SubmitHand<PERSON>, useForm } from "react-hook-form";
import { z } from "zod";
import { completeOnboarding } from "@/app/onboarding/_actions";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useOrganization } from "@/lib/contexts/organization-context";
import { useTRPC } from "../trpc/client";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Textarea } from "../ui/textarea";

interface Organization {
  id: string;
  name: string;
  tradeId: string | null;
  description: string;
  email: string;
  phone: string;
}

interface OrganizationFormProps {
  initialData?: Organization | null | undefined;
  onboarding?: boolean;
}

const organizationsSchema = z.object({
  name: z.string().min(1, "Name is required"),
  tradeId: z.string(),
  description: z.string(),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  phone: z.string().optional().or(z.literal("")),
});

type FormSchema = z.infer<typeof organizationsSchema>;

export function OrganizationForm({
  initialData,
  onboarding,
}: OrganizationFormProps) {
  const router = useRouter();
  const form = useForm<FormSchema>({
    resolver: zodResolver(organizationsSchema),
    defaultValues: {
      name: "",
      tradeId: "",
      description: "",
      email: "",
      phone: "",
    },
    values: {
      name: initialData?.name || "",
      tradeId: initialData?.tradeId || "",
      description: initialData?.description || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
    },
  });

  const trpc = useTRPC();
  const createOrganization = useMutation(
    trpc.organizations.create.mutationOptions()
  );
  const updateOrganization = useMutation(
    trpc.organizations.update.mutationOptions()
  );
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  const mutation = initialData ? updateOrganization : createOrganization;

  const { organizationList, setOrganizationList } = useOrganization();

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();

    const id = initialData?.id || "";

    mutation.mutate(
      { id, ...data },
      {
        onSuccess: async (data) => {
          organizationList?.push(data);
          setOrganizationList(organizationList);

          if (onboarding) {
            await completeOnboarding();
          }

          router.push("/dashboard");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating organization:", error);
        },
      }
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="max-w-md space-y-8"
      >
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="ACME Plumbing" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tradeId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Trade</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your trade..." />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {trades?.map((trade) => (
                    <SelectItem value={trade.id} key={trade.id}>
                      {trade.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email (Optional)</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="(*************" {...field} />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button type="submit">
            {initialData ? "Update" : "Create"} Organization
          </Button>
          <Button
            type="button"
            onClick={() => router.back()}
            variant="secondary"
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
