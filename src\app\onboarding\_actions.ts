"use server";

import { auth, clerkClient } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

export const completeOnboarding = async () => {
  const { userId } = await auth();
  const client = await clerkClient();

  if (!userId) {
    return;
  }

  try {
    const res = await client.users.updateUser(userId, {
      publicMetadata: { onboardingComplete: true },
    });

    return NextResponse.redirect(new URL("/dashboard"));
  } catch (err) {
    console.error(err);
  }
};
