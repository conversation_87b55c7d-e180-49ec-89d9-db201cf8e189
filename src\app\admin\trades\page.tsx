"use server";

import Link from "next/link";
import { Suspense } from "react";
import { Header } from "@/components/header";
import { TradeList } from "@/components/trade/trade-list";
import { HydrateClient, prefetch, trpc } from "@/components/trpc/server";
import { buttonVariants } from "@/components/ui/button";

export default async function TradesPage() {
  prefetch(trpc.trades.list.queryOptions());

  return (
    <>
      <Header title="Trades">
        <Link
          href="/admin/trades/new"
          className={buttonVariants({ variant: "default" })}
        >
          Add Trade
        </Link>
      </Header>
      <div className="p-8">
        <HydrateClient>
          <Suspense>
            <TradeList />
          </Suspense>
        </HydrateClient>
      </div>
    </>
  );
}
