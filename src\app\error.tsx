"use client";

import posthog from "posthog-js";
import { useEffect } from "react";

// biome-ignore lint/suspicious/noShadowRestrictedNames: This is the name Next.js expects
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    posthog.captureException(error);
  }, [error]);

  return (
    <main className="container flex h-full w-full flex-col items-center justify-center gap-12 px-4 py-16">
      <h1 className="font-extrabold text-4xl tracking-tight sm:text-5xl">
        Something went wrong!
      </h1>
      <button
        type="button"
        className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        onClick={() => reset()}
      >
        Try again
      </button>
    </main>
  );
}
