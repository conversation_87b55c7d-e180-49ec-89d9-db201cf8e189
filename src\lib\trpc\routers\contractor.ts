import { z } from "zod";
import { db } from "@/db";
import { protectedProcedure, router } from "../trpc";

export const contractorRouter = router({
  getStats: protectedProcedure
    .input(z.object({ organizationId: z.string() }).optional())
    .query(async ({ input, ctx }) => {
      // If no organizationId is provided, find the first organization for the user
      let organizationId = input?.organizationId;

      if (!organizationId) {
        const userOrg = await db.organization.findFirst({
          where: {
            memberships: {
              some: {
                userId: ctx.auth.userId,
              },
            },
          },
        });
        organizationId = userOrg?.id;
      }

      if (!organizationId) {
        return {
          totalBids: 0,
          activeJobs: 0,
          completedJobs: 0,
        };
      }

      // Count total bids by this organization
      const totalBids = await db.bid.count({
        where: {
          organizationId,
        },
      });

      // Count active jobs (jobs with accepted bids from this organization)
      const activeJobs = await db.job.count({
        where: {
          bids: {
            some: {
              organizationId,
              status: "ACCEPTED",
            },
          },
          status: "PUBLISHED",
        },
      });

      // Count completed jobs (jobs with accepted bids from this organization that are now closed or awarded)
      const completedJobs = await db.job.count({
        where: {
          bids: {
            some: {
              organizationId,
              status: "ACCEPTED",
            },
          },
          status: {
            in: ["CLOSED", "AWARDED"],
          },
        },
      });

      return {
        totalBids,
        activeJobs,
        completedJobs,
      };
    }),
});
