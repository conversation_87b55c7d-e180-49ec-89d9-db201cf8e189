import { currentUser } from "@clerk/nextjs/server";
import { format } from "date-fns";
import {
  BuildingIcon,
  CalendarIcon,
  ClockIcon,
  DollarSignIcon,
  HammerIcon,
  MailIcon,
  MapPinIcon,
  PhoneIcon,
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { StreamChat } from "stream-chat";
import { PusherChat } from "@/components/bid/pusher-chat";
import { Header } from "@/components/header";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { env } from "@/env";
import { getBidStatusVariant } from "@/lib/utils";

export default async function BidDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const user = await currentUser();
  const { id } = await params;

  if (!user) {
    redirect("/sign-in");
  }

  const serverClient = new StreamChat(env.STREAM_API_KEY, env.STREAM_SECRET);
  const userToken = serverClient.createToken(user.id);

  const queryClient = getQueryClient();
  const bid = await queryClient.fetchQuery(trpc.bids.one.queryOptions({ id }));

  if (!bid) {
    redirect("/dashboard");
  }

  const userType = user.unsafeMetadata.type as string;
  const isHomeowner = userType === "homeowner";

  return (
    <>
      <Header title={`Bid: ${bid.name}`}>
        <div className="flex gap-2">
          {isHomeowner && (
            <Button className="bg-green-600 hover:bg-green-700" asChild>
              <Link href={`/bids/${bid.id}/accept`}>Accept Bid</Link>
            </Button>
          )}
          <Button className="bg-orange-600 hover:bg-orange-700" asChild>
            <Link href={`/jobs/${bid.job.id}`}>Back to Project</Link>
          </Button>
        </div>
      </Header>

      <div className="p-8">
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Bid Details</CardTitle>
                  <Badge variant={getBidStatusVariant(bid.status)}>
                    {bid.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium">Project</h3>
                    <p className="text-muted-foreground">
                      {bid.job.name} at {bid.job.property.name}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-6">
                    <div className="flex items-center gap-2">
                      <DollarSignIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Bid Amount
                        </p>
                        <p className="font-medium">${bid.amount}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <ClockIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Estimated Duration
                        </p>
                        <p className="font-medium">
                          {bid.estimatedDuration} days
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Submitted On
                        </p>
                        <p className="font-medium">
                          {format(new Date(bid.createdAt), "PPP")}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="mb-2 font-medium">Bid Description</h3>
                    <div className="rounded-md bg-muted/50 p-4">
                      <p className="whitespace-pre-wrap">{bid.description}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Contractor Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="flex items-center gap-2 font-medium">
                      <BuildingIcon className="h-4 w-4 text-orange-500" />
                      Organization
                    </h3>
                    <p>{bid.organization.name}</p>
                  </div>

                  {bid.organization.trade && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <HammerIcon className="h-4 w-4 text-orange-500" />
                        Trade
                      </h3>
                      <p>{bid.organization.trade.name}</p>
                    </div>
                  )}

                  {bid.organization.address && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <MapPinIcon className="h-4 w-4 text-orange-500" />
                        Address
                      </h3>
                      <p>
                        {bid.organization.address.street},{" "}
                        {bid.organization.address.city},{" "}
                        {bid.organization.address.state}{" "}
                        {bid.organization.address.zip}
                      </p>
                    </div>
                  )}

                  {bid.organization.phone && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <PhoneIcon className="h-4 w-4 text-orange-500" />
                        Phone
                      </h3>
                      <p>{bid.organization.phone}</p>
                    </div>
                  )}

                  {bid.organization.email && (
                    <div>
                      <h3 className="flex items-center gap-2 font-medium">
                        <MailIcon className="h-4 w-4 text-orange-500" />
                        Email
                      </h3>
                      <p>{bid.organization.email}</p>
                    </div>
                  )}

                  {!bid.organization.phone &&
                    !bid.organization.email &&
                    !bid.organization.address && (
                      <div className="rounded-md bg-muted/50 p-4 text-muted-foreground text-sm">
                        <p>
                          No contact information available for this contractor.
                        </p>
                      </div>
                    )}
                </div>

                <div className="mt-6">
                  {isHomeowner && (
                    <Button
                      className="mb-2 w-full bg-green-600 hover:bg-green-700"
                      asChild
                    >
                      <Link href={`/bids/${bid.id}/accept`}>Accept Bid</Link>
                    </Button>
                  )}

                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/jobs/${bid.job.id}`}>Back to Project</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Add chat section */}
      <div className="mx-8 mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Messages</CardTitle>
          </CardHeader>
          <CardContent>
            <PusherChat bidId={bid.id} userId={user.id} />
          </CardContent>
        </Card>
      </div>
    </>
  );
}
