.stream-chat-custom-theme {
  /* Light/dark mode adaptive variables */
  --str-chat__primary-color: var(--primary);
  --str-chat__primary-color-alpha: var(--primary);
  --str-chat__secondary-color: var(--secondary);
  --str-chat__border-radius-sm: var(--radius-sm);
  --str-chat__border-radius-md: var(--radius-md);
  --str-chat__border-radius-lg: var(--radius-lg);
  --str-chat__border-radius-xl: var(--radius-xl);
  --str-chat__message-bubble-color: var(--muted);
  --str-chat__message-bubble-color-me: var(--primary);
  --str-chat__message-text-color: var(--foreground);
  --str-chat__message-text-color-me: var(--primary-foreground);
  --str-chat__input-bg-color: var(--background);
  --str-chat__input-border-color: var(--border);
  --str-chat__input-text-color: var(--secondary-foreground);
  --str-chat__cta-button-background: var(--primary);
  --str-chat__cta-button-text-color: var(--primary-foreground);
  --str-chat__avatar-border-color: var(--border);
  --str-chat__message-status-color: var(--muted-foreground);
  --str-chat__background-color: var(--card);
  --str-chat__message-textarea-color: var(--secondary-foreground);
}

/* Additional styling for message list */
.stream-chat-custom-theme .str-chat__message-list-scroll {
  background-color: var(--card);
}

.stream-chat-custom-theme .str-chat__message-simple {
  margin: 0.5rem 0;
  padding: 0.25rem 0;
}

.stream-chat-custom-theme .str-chat__message-simple-text {
  background-color: var(--accent);
  color: var(--accent-foreground);
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stream-chat-custom-theme .str-chat__message-simple-text-inner {
  color: var(--accent-foreground);
  font-weight: normal;
}

.stream-chat-custom-theme
  .str-chat__message-simple--me
  .str-chat__message-simple-text {
  background-color: var(--primary);
  color: var(--primary-foreground);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stream-chat-custom-theme
  .str-chat__message-simple--me
  .str-chat__message-simple-text-inner {
  color: var(--primary-foreground);
}

.stream-chat-custom-theme .str-chat__message-simple-status {
  color: var(--muted-foreground);
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Input styling - enhanced */
.stream-chat-custom-theme .str-chat__textarea {
  border-color: var(--border);
  color: var(--secondary-foreground);
}

.stream-chat-custom-theme .str-chat__input-flat {
  border-color: var(--border);
  border-top-width: 1px;
  background-color: var(--card);
}

.stream-chat-custom-theme .str-chat__send-button {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.stream-chat-custom-theme .str-chat__input-flat-tools {
  color: var(--muted-foreground);
}

/* Additional input styling fixes */
.stream-chat-custom-theme .str-chat__input-flat-wrapper {
  background-color: var(--card);
  border-color: var(--border);
}

.stream-chat-custom-theme .str-chat__input-flat-emojiselect {
  color: var(--muted-foreground);
}

.stream-chat-custom-theme .str-chat__input-flat-fileupload {
  color: var(--muted-foreground);
}

.stream-chat-custom-theme .str-chat__message-input {
  background-color: var(--card);
  border-color: var(--border);
}

.stream-chat-custom-theme .str-chat__input-flat-wrapper:focus-within {
  border-color: var(--ring);
}

.stream-chat-custom-theme .str-chat__textarea:focus {
  border-color: var(--ring);
}

/* Emoji picker and attachment menu */
.stream-chat-custom-theme .str-chat__emoji-picker {
  background-color: var(--popover);
  border-color: var(--border);
  color: var(--popover-foreground);
}

.stream-chat-custom-theme .str-chat__message-attachment-list {
  background-color: var(--background);
  border-color: var(--border);
}

/* Improve message list background */
.stream-chat-custom-theme .str-chat__list {
  background-color: var(--card);
  padding: 1rem;
}

/* Improve timestamp and sender name */
.stream-chat-custom-theme .str-chat__message-simple-name {
  color: var(--foreground);
  font-weight: 600;
}

.stream-chat-custom-theme .str-chat__message-simple-timestamp {
  color: var(--muted-foreground);
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Add a subtle border to the chat container */
.stream-chat-custom-theme .str-chat__container {
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

/* Improve dark mode contrast if needed */
.dark .stream-chat-custom-theme .str-chat__message-simple-text {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

.dark .stream-chat-custom-theme .str-chat__message-simple-text-inner {
  color: var(--primary-foreground);
}
