"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  BriefcaseIcon,
  CalendarIcon,
  DollarSignIcon,
  ListTodoIcon,
  PlusIcon,
  TrashIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { type SubmitHandler, useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useJob } from "@/lib/contexts/job-context";
import { useOrganization } from "@/lib/contexts/organization-context";
import { cn } from "@/lib/utils";
import { useTRPC } from "../trpc/client";
import { Calendar } from "../ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { ScrollArea } from "../ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Separator } from "../ui/separator";
import { JobImageUploader } from "./job-image-uploader";

interface Task {
  id: string;
  name: string;
  tradeId: string;
}

interface Job {
  id: string;
  name: string | undefined;
  tasks: Task[];
  startsAt: Date;
  deadline: Date;
  budget: number;
  images?: { url: string; description: string }[];
}

interface JobFormProps {
  initialData?: Job | null | undefined;
  propertyId?: string;
}

const jobsSchema = z.object({
  name: z.string().min(1, "Name is required"),
  startsAt: z.date(),
  deadline: z.date(),
  budget: z.coerce.number(),
  propertyId: z.string().min(1, "Property is required"),
  tasks: z.object({ name: z.string(), tradeId: z.string() }).array(),
  images: z.object({ url: z.string(), description: z.string() }).array(),
});

type FormSchema = z.infer<typeof jobsSchema>;

export function JobForm({ initialData, propertyId }: JobFormProps) {
  const router = useRouter();
  const form = useForm<FormSchema>({
    resolver: zodResolver(jobsSchema),
    defaultValues: initialData
      ? { ...initialData }
      : {
          name: "",
          tasks: [],
          budget: 0,
          propertyId: propertyId || "",
          startsAt: undefined,
          deadline: undefined,
          images: [],
        },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "tasks",
  });

  const trpc = useTRPC();
  const createJob = useMutation(trpc.jobs.create.mutationOptions());
  const updateJob = useMutation(trpc.jobs.update.mutationOptions());
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());
  const { data: properties } = useQuery(
    trpc.properties.list.queryOptions({
      organizationId: useOrganization().organization?.id || "",
    })
  );

  const mutation = initialData ? updateJob : createJob;

  const { jobList, setJobList } = useJob();

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();

    const id = initialData?.id || "";

    mutation.mutate(
      { id, ...data },
      {
        onSuccess: (data) => {
          jobList?.push(data);
          setJobList(jobList);
          router.push("/dashboard");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating job:", error);
        },
      }
    );
  };

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <ScrollArea className="flex-1">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="mx-auto max-w-2/3 space-y-8"
          >
            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <BriefcaseIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Project Details</h3>
              </div>
              <Separator className="mb-6" />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Project Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Kitchen Remodel"
                        {...field}
                        className="focus-visible:ring-orange-500"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!propertyId && (
                <FormField
                  control={form.control}
                  name="propertyId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Property</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="focus-visible:ring-orange-500">
                            <SelectValue placeholder="Select a property" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {properties?.map((property) => (
                            <SelectItem key={property.id} value={property.id}>
                              {property.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <ListTodoIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Tasks</h3>
                <div className="flex-1" />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => append({ name: "", tradeId: "" })}
                  className="flex items-center gap-1"
                >
                  <PlusIcon className="h-4 w-4" />
                  Add Task
                </Button>
              </div>
              <Separator className="mb-6" />

              {fields.length === 0 && (
                <div className="rounded-lg border border-muted-foreground/20 border-dashed p-8 text-center">
                  <p className="text-muted-foreground text-sm">
                    No tasks added yet.
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => append({ name: "", tradeId: "" })}
                    className="mt-4 flex items-center gap-1"
                  >
                    <PlusIcon className="h-4 w-4" />
                    Add Your First Task
                  </Button>
                </div>
              )}

              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div
                    key={field.id}
                    className="space-y-4 rounded-md border p-4"
                  >
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Task {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => remove(index)}
                        className="h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name={`tasks.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Task Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Install Cabinets"
                                {...field}
                                className="focus-visible:ring-orange-500"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`tasks.${index}.tradeId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Trade</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="focus-visible:ring-orange-500">
                                  <SelectValue placeholder="Select a trade" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {trades?.map((trade) => (
                                  <SelectItem key={trade.id} value={trade.id}>
                                    {trade.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <DollarSignIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Budget</h3>
              </div>
              <Separator className="mb-6" />

              <FormField
                control={form.control}
                name="budget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget Amount</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute top-1.5 left-3 text-muted-foreground">
                          $
                        </span>
                        <Input
                          type="number"
                          placeholder="1000"
                          {...field}
                          className="pl-7 focus-visible:ring-orange-500"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <CalendarIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Timeline</h3>
              </div>
              <Separator className="mb-6" />

              <div className="grid gap-6 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="startsAt"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Start Date</FormLabel>
                      <Popover modal={true}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal focus-visible:ring-orange-500",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto size-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-auto p-0"
                          align="start"
                          side="bottom"
                        >
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deadline"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Deadline</FormLabel>
                      <Popover modal={true}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal focus-visible:ring-orange-500",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto size-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-auto p-0"
                          align="start"
                          side="bottom"
                        >
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="mb-6">
              <FormField
                control={form.control}
                name="images"
                render={({ field }) => (
                  <FormItem>
                    <JobImageUploader
                      initialImages={field.value}
                      onChange={field.onChange}
                      maxImages={10}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mt-8 flex justify-end gap-4 pb-4">
              <Button
                type="button"
                onClick={() => router.back()}
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-orange-600 hover:bg-orange-700"
              >
                {initialData ? "Update" : "Create"} Project
              </Button>
            </div>
          </form>
        </Form>
      </ScrollArea>
    </div>
  );
}
