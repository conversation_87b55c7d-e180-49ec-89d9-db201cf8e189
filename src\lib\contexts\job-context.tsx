"use client";

import { useQuery } from "@tanstack/react-query";
import {
  createContext,
  Suspense,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTRPC } from "@/components/trpc/client";
import type { Job } from "@/db/generated";

export type JobContextType = {
  job: Job | null | undefined;
  setJob: (job: Job) => void;
  jobList: Job[] | null | undefined;
  setJobList: (
    jobList: Job[] | null | undefined
  ) => void;
};

const JobContext = createContext<JobContextType>({
  job: null,
  setJob: () => {},
  jobList: null,
  setJobList: () => {},
});

function JobLoader({
  setJob,
  setJobList,
}: {
  setJob: (job: Job) => void;
  setJobList: (jobList: Job[]) => void;
}) {
  const trpc = useTRPC();
//   const { data: defaultOrganization } = useQuery(
//     trpc.jobs.firstForUser.queryOptions()
//   );
  const { data: jobs } = useQuery(
    trpc.jobs.listForUser.queryOptions()
  );

  useEffect(() => {
    if (jobs) {
      setJobList(jobs);
    }
  }, [jobs, setJobList]);

//   useEffect(() => {
//     if (defaultJob) {
//       setJob(defaultJob);
//     }
//   }, [defaultJob, setJob]);

  return null;
}

export function JobProvider({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const [job, setJob] = useState<
    Job | null | undefined
  >(null);
  const [jobList, setJobList] = useState<
    Job[] | null | undefined
  >(null);

  const value = {
    job,
    setJob,
    jobList,
    setJobList,
  };

  return (
    <JobContext.Provider value={value}>
      <Suspense fallback={null}>
        <JobLoader
          setJob={setJob}
          setJobList={setJobList}
        />
      </Suspense>
      {children}
    </JobContext.Provider>
  );
}

export function useJob() {
  return useContext(JobContext);
}
