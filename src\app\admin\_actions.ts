import { clerkClient } from "@clerk/nextjs/server";
import { cache } from "react";
import type { User } from "@/components/users/columns";
import { checkRole } from "@/lib/roles";

export async function setRole(formData: FormData) {
  const client = await clerkClient();

  if (!checkRole("admin")) {
    return { message: "Unauthorized" };
  }

  try {
    const res = await client.users.updateUserMetadata(
      formData.get("id") as string,
      {
        publicMetadata: { role: formData.get("role") as string },
      },
    );

    return { message: res.publicMetadata };
  } catch (err) {
    return { message: err };
  }
}

export async function removeRole(formData: FormData) {
  const client = await clerkClient();

  try {
    const res = await client.users.updateUserMetadata(
      formData.get("id") as string,
      {
        publicMetadata: { role: null },
      },
    );

    return { message: res.publicMetadata };
  } catch (err) {
    return { message: err };
  }
}

export async function getUsers({ query }: { query?: string }): Promise<User[]> {
  const client = await clerkClient();

  const users = (
    await client.users.getUserList({
      limit: 100,
      query,
    })
  ).data.map((user) => {
    return {
      id: user.id,
      name: `${user.firstName} ${user.lastName}`,
      email: user.emailAddresses[0]?.emailAddress || "",
      isAdmin: (user.publicMetadata.admin as boolean) || false,
      avatar: user.imageUrl,
    };
  });

  return users;
}

export const getCachedUsers = cache(getUsers);
