import { DataTable } from "@/components/data-table";
import { Header } from "@/components/header";
import { columns } from "@/components/users/columns";
import { getCachedUsers } from "../_actions";

export default async function UsersPage(params: {
  searchParams: Promise<{ search?: string }>;
}) {
  const { search } = await params.searchParams;

  const users = await getCachedUsers({ query: search });

  return (
    <>
      <Header title="Users" />
      <div className="p-4">
        <DataTable columns={columns} data={users} />
      </div>
    </>
  );
}
