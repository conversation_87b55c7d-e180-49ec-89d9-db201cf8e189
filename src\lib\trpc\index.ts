import { createContext } from "./context";
import { bidsRouter } from "./routers/bids";
import { contractorRouter } from "./routers/contractor";
import { jobsRouter } from "./routers/jobs";
import { messagesRouter } from "./routers/messages";
import { organizationsRouter } from "./routers/organizations";
import { propertiesRouter } from "./routers/properties";
import { tradesRouter } from "./routers/trades";
import { usersRouter } from "./routers/users";
import { router } from "./trpc";

export const appRouter = router({
  bids: bidsRouter,
  trades: tradesRouter,
  contractor: contractorRouter,
  organizations: organizationsRouter,
  jobs: jobsRouter,
  users: usersRouter,
  properties: propertiesRouter,
  messages: messagesRouter,
});

export type AppRouter = typeof appRouter;

export const caller = appRouter.createCaller(createContext);
