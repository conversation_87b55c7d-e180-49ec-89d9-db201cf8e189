import { Header } from "@/components/header";
import { JobForm } from "@/components/job/job-form";
import { getQueryClient, trpc } from "@/components/trpc/server";

export default async function EditJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const queryClient = getQueryClient();
  const job = await queryClient.fetchQuery(trpc.jobs.one.queryOptions({ id }));

  if (!job) {
    throw new Error("Job not found");
  }

  return (
    <>
      <Header title="Edit Job" />
      <div className="p-8">
        <JobForm initialData={job} propertyId={job.property.id} />
      </div>
    </>
  );
}
