import "server-only";

import { clerkClient } from "@clerk/nextjs/server";
import { cache } from "react";
import { z } from "zod";
import { adminProcedure, router } from "../trpc";

const getUser = cache(async (id: string) => {
  const client = await clerkClient();

  const user = await client.users.getUser(id);

  return user;
});

export const usersRouter = router({
  setAdmin: adminProcedure
    .input(z.object({ id: z.string(), admin: z.boolean() }))
    .mutation(async ({ input }) => {
      const client = await clerkClient();

      try {
        client.users.updateUserMetadata(input.id, {
          publicMetadata: { admin: input.admin },
        });
      } catch (error) {
        console.error(error);
      }
    }),
  getAdmin: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      try {
        const user = await getUser(input.id);
        return (user.publicMetadata.admin as boolean) || false;
      } catch (error) {
        return false;
      }
    }),
});
