"use client";

import "@uppy/core/dist/style.min.css";
import "@uppy/dashboard/dist/style.min.css";

import { useUser } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import Uppy from "@uppy/core";
import { Dashboard, useUppyEvent } from "@uppy/react";
import Transloadit from "@uppy/transloadit";
import {
  ChevronsUpDownIcon,
  HomeIcon,
  ImageIcon,
  MapPinIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { type SubmitHandler, useForm } from "react-hook-form";
import { toast } from "sonner";
import states from "states-us";
import validator from "validator";
import { z } from "zod";
import { completeOnboarding } from "@/app/onboarding/_actions";
import { env } from "@/env";
import { useOrganization } from "@/lib/contexts/organization-context";
import { cn } from "@/lib/utils";
import { useTRPC } from "../trpc/client";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { DrawerClose } from "../ui/drawer";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { ScrollArea } from "../ui/scroll-area";
import { Separator } from "../ui/separator";

const propertySchema = z.object({
  name: z.string().min(1, "Name is required"),
  imageUrl: z.string(),
  organizationId: z.string(),
  address: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zip: z
      .string()
      .min(1, "Zip is required")
      .refine((value) => validator.isPostalCode(value, "US"), {
        message: "Please enter a valid US ZIP code",
      }),
  }),
});

type FormSchema = z.infer<typeof propertySchema>;

function createUppy() {
  const uppy = new Uppy({
    restrictions: {
      maxNumberOfFiles: 1,
      allowedFileTypes: ["image/*"],
    },
    meta: {
      type: "property-image",
      userId: "",
    },
  });

  uppy.use(Transloadit, {
    async assemblyOptions() {
      const { meta } = uppy.getState();
      const body = JSON.stringify({ userId: meta.userId });
      const res = await fetch("/api/transloadit", { method: "POST", body });
      return res.json();
    },
    waitForEncoding: true,
  });

  return uppy;
}

export function PropertyForm({
  onboarding,
  onSuccess,
}: Readonly<{
  onboarding?: boolean;
  onSuccess?: () => void;
}>) {
  const router = useRouter();
  const { organization } = useOrganization();
  const [uppy] = useState(createUppy);
  const { user } = useUser();
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const drawerCloseRef = useRef<HTMLButtonElement>(null);

  const createProperty = useMutation(
    trpc.properties.create.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: trpc.properties.list.queryKey(),
        });
      },
    })
  );

  const createOrganization = useMutation(
    trpc.organizations.create.mutationOptions()
  );

  const [imageUrl, setImageUrl] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useUppyEvent(uppy, "transloadit:result", (stepName, result) => {
    if (stepName === "compressed-image") {
      const url = `${env.NEXT_PUBLIC_STORAGE_URL}${result.name}`;
      setImageUrl(url);
    }
  });

  useEffect(() => {
    if (user) {
      uppy.setOptions({ meta: { userId: user.id } });
    }
  }, [uppy, user]);

  const form = useForm<FormSchema>({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      name: "",
      imageUrl: "",
      organizationId: "",
      address: {
        street: "",
        city: "",
        state: "",
        zip: "",
      },
    },
  });

  const onSubmit: SubmitHandler<FormSchema> = async (data, e) => {
    e?.preventDefault();
    setIsSubmitting(true);

    try {
      if (onboarding) {
        createOrganization.mutate(
          { name: "Personal", tradeId: "" },
          {
            onSuccess: (org) => {
              data.organizationId = org.id;
            },
          }
        );
      } else {
        data.organizationId = organization?.id || "";
      }

      data.imageUrl = imageUrl;

      createProperty.mutate(data, {
        onSuccess: async () => {
          if (onboarding) {
            await completeOnboarding();
          }

          toast.success("Property created", {
            description: `${data.name} has been successfully created.`,
          });

          // Close the drawer if it exists
          if (drawerCloseRef.current) {
            drawerCloseRef.current.click();
          }

          // Call the onSuccess callback if provided
          if (onSuccess) {
            onSuccess();
          }

          router.push("/properties");
          router.refresh();
        },
        onError: (error) => {
          console.error("Error creating property:", error);
          toast.error("Error", {
            description: "Failed to create property. Please try again.",
          });
        },
        onSettled: () => {
          setIsSubmitting(false);
        },
      });
    } catch (error) {
      console.error("Error in form submission:", error);
      setIsSubmitting(false);
      toast.error("Error", {
        description: "Something went wrong. Please try again.",
      });
    }
  };

  return (
    <div className="overflow-y-scroll p-6">
      <Form {...form}>
        <form id="property-form" onSubmit={form.handleSubmit(onSubmit)}>
          <div className="mx-auto max-w-2/3">
            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <HomeIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Property Details</h3>
              </div>
              <Separator className="mb-6" />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Property Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g. Beach House, Rental Property"
                        className="focus-visible:ring-orange-500"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <MapPinIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Address</h3>
              </div>
              <Separator className="mb-6" />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="address.street"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Street Address</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="address-line1"
                          placeholder="123 Main St"
                          className="focus-visible:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          autoComplete="address-level2"
                          placeholder="San Francisco"
                          className="focus-visible:ring-orange-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="address.state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                // biome-ignore lint/a11y/useSemanticElements: Custom combobox
                                role="combobox"
                                className={cn(
                                  "w-full justify-between",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value
                                  ? states.find(
                                      (state) =>
                                        state.abbreviation === field.value
                                    )?.name
                                  : "Select state"}
                                <ChevronsUpDownIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search state..."
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>No state found.</CommandEmpty>
                                <CommandGroup>
                                  {states.map((state) => (
                                    <CommandItem
                                      value={state.abbreviation}
                                      key={state.abbreviation}
                                      onSelect={(value) => {
                                        form.setValue("address.state", value);
                                      }}
                                    >
                                      {state.name}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address.zip"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ZIP Code</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            autoComplete="postal-code"
                            placeholder="94103"
                            className="focus-visible:ring-orange-500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-orange-500" />
                <h3 className="font-medium text-lg">Property Image</h3>
              </div>
              <Separator className="mb-6" />

              <div className="rounded-lg bg-muted/30 p-4">
                <Dashboard
                  uppy={uppy}
                  height={250}
                  width="100%"
                  theme="auto"
                  proudlyDisplayPoweredByUppy={false}
                  showLinkToFileUploadResult={false}
                  showProgressDetails
                  note="Images should be in JPG or PNG format, max 10MB"
                />
                {imageUrl && (
                  <div className="mt-2 text-green-600 text-sm">
                    ✓ Image uploaded successfully
                  </div>
                )}
              </div>
            </div>

            <div className="mt-8 flex justify-end gap-4 pb-4">
              <DrawerClose ref={drawerCloseRef} asChild>
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </DrawerClose>
              <Button
                type="submit"
                disabled={isSubmitting || !imageUrl}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {isSubmitting ? "Creating..." : "Create Property"}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
