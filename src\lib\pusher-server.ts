import "server-only";

import Pusher from "pusher";
import { env } from "@/env";

let pusherInstance: Pusher | null = null;

export function getPusherServer() {
  if (!pusherInstance) {
    pusherInstance = new Pusher({
      appId: env.PUSHER_APP_ID,
      key: env.PUSHER_KEY,
      secret: env.PUSHER_SECRET,
      cluster: env.PUSHER_CLUSTER,
      useTLS: true,
    });
  }

  return pusherInstance;
}

// Helper function to trigger chat events
export async function triggerChatEvent(
  chatId: string,
  event: string,
  data: any
) {
  const pusher = getPusherServer();
  
  try {
    await pusher.trigger(`private-chat-${chatId}`, event, data);
  } catch (error) {
    console.error("Error triggering Pusher event:", error);
    throw error;
  }
}

// Helper function to trigger typing events
export async function triggerTypingEvent(
  chatId: string,
  userId: string,
  isTyping: boolean
) {
  const pusher = getPusherServer();
  
  try {
    await pusher.trigger(`private-chat-${chatId}`, "typing", {
      userId,
      isTyping,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error triggering typing event:", error);
    throw error;
  }
}

// Helper function to trigger presence events
export async function triggerPresenceEvent(
  chatId: string,
  userId: string,
  status: "online" | "offline"
) {
  const pusher = getPusherServer();
  
  try {
    await pusher.trigger(`private-chat-${chatId}`, "presence", {
      userId,
      status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error triggering presence event:", error);
    throw error;
  }
}
