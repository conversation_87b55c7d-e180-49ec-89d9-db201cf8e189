"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { getPusherClient } from "@/lib/pusher-client";

export function ChatTest() {
  const [isConnected, setIsConnected] = useState(false);
  const [testMessage, setTestMessage] = useState("");
  const [connectionStatus, setConnectionStatus] = useState("Disconnected");

  const testPusherConnection = () => {
    try {
      const pusher = getPusherClient();
      
      pusher.connection.bind("connected", () => {
        setIsConnected(true);
        setConnectionStatus("Connected");
      });

      pusher.connection.bind("disconnected", () => {
        setIsConnected(false);
        setConnectionStatus("Disconnected");
      });

      pusher.connection.bind("error", (error: any) => {
        setIsConnected(false);
        setConnectionStatus(`Error: ${error.message}`);
      });

      // Try to connect
      if (pusher.connection.state === "disconnected") {
        pusher.connect();
      }
    } catch (error) {
      setConnectionStatus(`Failed to initialize: ${error}`);
    }
  };

  const testMessageSend = async () => {
    if (!testMessage.trim()) return;

    try {
      const response = await fetch("/api/pusher/typing", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          chatId: "test-chat",
          userId: "test-user",
          isTyping: true,
        }),
      });

      if (response.ok) {
        setTestMessage("");
        alert("Test message sent successfully!");
      } else {
        const error = await response.text();
        alert(`Failed to send test message: ${error}`);
      }
    } catch (error) {
      alert(`Error sending test message: ${error}`);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Chat System Test
          <Badge variant={isConnected ? "default" : "destructive"}>
            {connectionStatus}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Button onClick={testPusherConnection} className="w-full">
            Test Pusher Connection
          </Button>
        </div>

        <div className="space-y-2">
          <Input
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Test message..."
          />
          <Button 
            onClick={testMessageSend} 
            className="w-full"
            disabled={!testMessage.trim()}
          >
            Test Message Send
          </Button>
        </div>

        <div className="text-sm text-muted-foreground">
          <p><strong>Status:</strong> {connectionStatus}</p>
          <p><strong>Connected:</strong> {isConnected ? "Yes" : "No"}</p>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>This component tests the Pusher connection and basic functionality.</p>
          <p>Make sure your Pusher environment variables are configured correctly.</p>
        </div>
      </CardContent>
    </Card>
  );
}
