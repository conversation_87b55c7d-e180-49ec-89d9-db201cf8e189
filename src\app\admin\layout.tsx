"use client";

import {
  ArrowLeftIcon,
  ClipboardListIcon,
  DrillIcon,
  LayoutDashboardIcon,
  User2Icon,
} from "lucide-react";
import type { Metadata } from "next";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

const menu = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: LayoutDashboardIcon,
  },
  {
    title: "Trades",
    href: "/admin/trades",
    icon: DrillIcon,
  },
  {
    title: "Jobs",
    href: "/admin/jobs",
    icon: ClipboardListIcon,
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: User2Icon,
  },
  {
    title: "User Dashboard",
    href: "/dashboard",
    icon: ArrowLeftIcon,
  },
];

export default function AdminLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <SidebarProvider>
      <AppSidebar menu={menu} />
      <SidebarInset>{children}</SidebarInset>
    </SidebarProvider>
  );
}
