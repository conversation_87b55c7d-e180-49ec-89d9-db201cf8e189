/*
  Warnings:

  - You are about to drop the column `draft` on the `Job` table. All the data in the column will be lost.

*/
-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "JobStatus" AS ENUM ('DRAFT', 'PUBLISHED', 'CLOSED', 'CANCELED', 'AWARDED');

-- Create<PERSON>num
CREATE TYPE "BidStatus" AS ENUM ('PROPOSED', 'ACCEPTED', 'REJECTED', 'CANCELED');

-- AlterTable
ALTER TABLE "Bid" ADD COLUMN     "status" "BidStatus" NOT NULL DEFAULT 'PROPOSED';

-- AlterTable
ALTER TABLE "Job" DROP COLUMN "draft",
ADD COLUMN     "status" "JobStatus" NOT NULL DEFAULT 'DRAFT';
