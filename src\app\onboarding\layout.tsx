import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { ErrorBoundary } from "react-error-boundary";

export default async function OnboardingLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  if ((await auth()).sessionClaims?.metadata.onboardingComplete === true) {
    redirect("/");
  }

  return (
    <>
      <ErrorBoundary fallback={<div>Something went wrong</div>}>
        {children}
      </ErrorBoundary>
    </>
  );
}
