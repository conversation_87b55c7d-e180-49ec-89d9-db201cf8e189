import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { getPusherServer } from "@/lib/pusher-server";

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.text();
    const params = new URLSearchParams(body);
    const socketId = params.get("socket_id");
    const channelName = params.get("channel_name");

    if (!socketId || !channelName) {
      return NextResponse.json(
        { error: "Missing socket_id or channel_name" },
        { status: 400 }
      );
    }

    // Extract chatId from channel name (format: private-chat-{chatId})
    const chatIdMatch = channelName.match(/^private-chat-(.+)$/);
    if (!chatIdMatch) {
      return NextResponse.json(
        { error: "Invalid channel name format" },
        { status: 400 }
      );
    }

    const chatId = chatIdMatch[1];

    // Verify user has access to this chat
    const chat = await db.chat.findUnique({
      where: { id: chatId },
      include: {
        bid: {
          include: {
            job: {
              include: {
                property: {
                  include: {
                    organization: {
                      include: {
                        memberships: true,
                      },
                    },
                  },
                },
              },
            },
            organization: {
              include: {
                memberships: true,
              },
            },
          },
        },
      },
    });

    if (!chat) {
      return NextResponse.json({ error: "Chat not found" }, { status: 404 });
    }

    // Check if user is a member of either the property owner's organization or the bidder's organization
    const propertyOwnerMemberships = chat.bid.job.property.organization.memberships;
    const bidderMemberships = chat.bid.organization.memberships;

    const isPropertyOwner = propertyOwnerMemberships.some(
      (membership) => membership.userId === userId
    );
    const isBidder = bidderMemberships.some(
      (membership) => membership.userId === userId
    );

    if (!isPropertyOwner && !isBidder) {
      return NextResponse.json(
        { error: "Access denied to this chat" },
        { status: 403 }
      );
    }

    // Authenticate the user for this private channel
    const pusher = getPusherServer();
    const authResponse = pusher.authorizeChannel(socketId, channelName, {
      user_id: userId,
      user_info: {
        name: userId, // You can enhance this with actual user name from Clerk
        role: isPropertyOwner ? "homeowner" : "professional",
      },
    });

    return NextResponse.json(authResponse);
  } catch (error) {
    console.error("Pusher auth error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
