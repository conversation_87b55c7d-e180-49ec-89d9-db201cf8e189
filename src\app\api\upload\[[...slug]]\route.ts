import { FileStore } from "@tus/file-store";
import { Server } from "@tus/server";

const server = new Server({
  path: "/api/upload",
  datastore: new FileStore({ directory: "./uploads" }),
});

export const GET = server.handleWeb;
export const POST = server.handleWeb;
export const PATCH = server.handleWeb;
export const HEAD = server.handleWeb;
export const OPTIONS = server.handleWeb;
export const DELETE = server.handleWeb;
