{"name": "tradecrews-next", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "postinstall": "prisma generate", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "commit": "cz"}, "dependencies": {"@clerk/elements": "^0.23.29", "@clerk/nextjs": "^6.20.0", "@hookform/resolvers": "^5.0.1", "@icons-pack/react-simple-icons": "^12.8.0", "@neondatabase/serverless": "^1.0.0", "@prisma/adapter-neon": "^6.8.2", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "@react-email/components": "^0.0.36", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.76.2", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.1.2", "@trpc/server": "^11.1.2", "@trpc/tanstack-react-query": "^11.1.2", "@tus/file-store": "^2.0.0", "@tus/server": "^2.2.0", "@uppy/core": "^4.4.5", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/form": "^4.1.1", "@uppy/progress-bar": "^4.2.1", "@uppy/react": "^4.2.3", "@uppy/transloadit": "^4.2.2", "@uppy/tus": "^4.2.2", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "contentlayer": "^0.3.4", "date-fns": "^4.1.0", "lucide-react": "^0.487.0", "next": "^15.3.2", "next-contentlayer": "^0.3.4", "next-themes": "^0.4.6", "posthog-js": "^1.246.0", "posthog-node": "^4.17.2", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-inlinesvg": "^4.2.0", "recharts": "^2.15.3", "server-only": "^0.0.1", "sonner": "^2.0.3", "states-us": "^1.1.1", "stream-chat": "^9.1.1", "stream-chat-react": "^13.0.4", "superjson": "^2.2.2", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0", "validator": "^13.15.0", "vaul": "^1.1.2", "ws": "^8.18.2", "zod": "^3.25.23"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.2", "@paralleldrive/cuid2": "^2.2.2", "@snaplet/copycat": "^6.0.0", "@snaplet/seed": "0.98.0", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/validator": "^13.15.1", "@types/ws": "^8.18.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "3.3.0", "postcss": "^8.5.3", "prisma": "^6.8.2", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}, "ct3aMetadata": {"initVersion": "7.39.2"}, "packageManager": "pnpm@10.11.0", "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "prisma": {"seed": "pnpx tsx src/lib/seed/seed.ts"}, "@snaplet/seed": {"config": "src/lib/seed/seed.config.ts"}}