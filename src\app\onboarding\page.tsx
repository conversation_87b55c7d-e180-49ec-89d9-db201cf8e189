"use client";

import { useUser } from "@clerk/nextjs";
import { HomeownerOnboarding } from "@/components/homeowner-onboarding";
import { ProfessionalOnboarding } from "@/components/professional-onboarding";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function OnboardingPage() {
  const { user } = useUser();

  return (
    <div className="flex h-full items-center justify-center">
      <Card className="w-[56rem]">
        <CardHeader>
          <CardTitle>Welcome to TradeCrews.</CardTitle>
          <CardDescription>
            You're on your way to a better home.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {user?.unsafeMetadata.type === "homeowner" && <HomeownerOnboarding />}
          {user?.unsafeMetadata.type === "professional" && (
            <ProfessionalOnboarding />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
