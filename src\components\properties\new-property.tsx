"use client";

import { PlusIcon } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "../ui/drawer";
import { PropertyForm } from "./property-form";

export function NewProperty() {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
  };

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <Card
          id="new-property-trigger"
          className="h-full min-h-[280px] cursor-pointer border-2 border-orange-200 border-dashed bg-orange-50/50 transition-colors hover:border-orange-300 hover:bg-orange-50"
        >
          <CardContent className="flex h-full flex-col items-center justify-center p-6">
            <div className="rounded-full bg-orange-100 p-3">
              <PlusIcon className="h-6 w-6 text-orange-600" />
            </div>
            <h3 className="mt-4 font-medium">Add Property</h3>
            <p className="mt-1 text-center text-muted-foreground text-sm">
              Create a new property to manage your projects
            </p>
          </CardContent>
        </Card>
      </DrawerTrigger>
      <DrawerContent className="p-4">
        <DrawerHeader>
          <DrawerTitle>New Property</DrawerTitle>
          <DrawerDescription>
            Add a new property to your organization.
          </DrawerDescription>
        </DrawerHeader>
        <PropertyForm onSuccess={handleSuccess} />
      </DrawerContent>
    </Drawer>
  );
}
