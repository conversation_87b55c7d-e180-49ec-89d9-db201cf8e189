"use client";

import Pusher from "pusher-js";
import { env } from "@/env";

let pusherInstance: Pusher | null = null;

export function getPusherClient() {
  if (!pusherInstance) {
    pusherInstance = new Pusher(env.NEXT_PUBLIC_PUSHER_KEY, {
      cluster: env.NEXT_PUBLIC_PUSHER_CLUSTER,
      authEndpoint: "/api/pusher/auth",
      auth: {
        headers: {
          "Content-Type": "application/json",
        },
      },
    });

    // Enable Pusher logging in development
    if (env.NODE_ENV === "development") {
      Pusher.logToConsole = true;
    }
  }

  return pusherInstance;
}

// Helper function to subscribe to a chat channel
export function subscribeToChatChannel(
  chatId: string,
  callbacks: {
    onMessage?: (data: any) => void;
    onTyping?: (data: any) => void;
    onPresence?: (data: any) => void;
    onSubscriptionSucceeded?: () => void;
    onSubscriptionError?: (error: any) => void;
  }
) {
  const pusher = getPusherClient();
  const channelName = `private-chat-${chatId}`;
  
  // Check if already subscribed
  const existingChannel = pusher.channel(channelName);
  if (existingChannel) {
    return existingChannel;
  }

  const channel = pusher.subscribe(channelName);

  // Bind event handlers
  if (callbacks.onMessage) {
    channel.bind("message", callbacks.onMessage);
  }

  if (callbacks.onTyping) {
    channel.bind("typing", callbacks.onTyping);
  }

  if (callbacks.onPresence) {
    channel.bind("presence", callbacks.onPresence);
  }

  if (callbacks.onSubscriptionSucceeded) {
    channel.bind("pusher:subscription_succeeded", callbacks.onSubscriptionSucceeded);
  }

  if (callbacks.onSubscriptionError) {
    channel.bind("pusher:subscription_error", callbacks.onSubscriptionError);
  }

  return channel;
}

// Helper function to unsubscribe from a chat channel
export function unsubscribeFromChatChannel(chatId: string) {
  const pusher = getPusherClient();
  const channelName = `private-chat-${chatId}`;
  
  pusher.unsubscribe(channelName);
}

// Helper function to disconnect Pusher
export function disconnectPusher() {
  if (pusherInstance) {
    pusherInstance.disconnect();
    pusherInstance = null;
  }
}

// Helper function to send typing indicator
export function sendTypingIndicator(
  chatId: string,
  userId: string,
  isTyping: boolean
) {
  // This will be handled by the server-side API
  // We'll create an endpoint for this
  fetch("/api/pusher/typing", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      userId,
      isTyping,
    }),
  }).catch((error) => {
    console.error("Error sending typing indicator:", error);
  });
}

// Helper function to update presence
export function updatePresence(
  chatId: string,
  userId: string,
  status: "online" | "offline"
) {
  fetch("/api/pusher/presence", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatId,
      userId,
      status,
    }),
  }).catch((error) => {
    console.error("Error updating presence:", error);
  });
}
