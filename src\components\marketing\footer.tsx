import { SiFacebook, SiInstagram, SiX } from "@icons-pack/react-simple-icons";

const navigation = [
  {
    name: "Facebook",
    href: "#",
    icon: (props: React.ComponentProps<typeof SiFacebook>) => (
      <SiFacebook {...props} />
    ),
  },
  {
    name: "Instagram",
    href: "#",
    icon: (props: React.ComponentProps<typeof SiInstagram>) => (
      <SiInstagram {...props} />
    ),
  },
  {
    name: "X",
    href: "#",
    icon: (props: React.ComponentProps<typeof SiX>) => <SiX {...props} />,
  },
];

export default function Example() {
  return (
    <footer className="bg-white">
      <div className="mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8">
        <div className="flex justify-center gap-x-6 md:order-2">
          {navigation.map((item) => (
            <a
              key={item.name}
              href={item.href}
              className="text-gray-600 hover:text-gray-800"
            >
              <span className="sr-only">{item.name}</span>
              <item.icon aria-hidden="true" className="size-6" />
            </a>
          ))}
        </div>
        <p className="mt-8 text-center text-gray-600 text-sm/6 md:order-1 md:mt-0">
          &copy; 2025 TradeCrews Inc. All rights reserved.
        </p>
      </div>
    </footer>
  );
}
