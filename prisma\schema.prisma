// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider            = "prisma-client"
  output              = "../src/db/generated"
  importFileExtension = ""
  previewFeatures     = ["driverAdapters"]
}

datasource db {
  provider  = "postgresql"
  // NOTE: When using mysql or sqlserver, uncomment the @db.Text annotations in model Account below
  // Further reading:
  // https://next-auth.js.org/adapters/prisma#create-the-prisma-schema
  // https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Necessary for Next auth
model Membership {
  userId         String
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  role           String

  @@id([userId, organizationId])
  @@unique([userId, organizationId])
}

model Organization {
  id          String       @id @default(cuid())
  name        String
  properties  Property[]
  bids        Bid[]
  trade       Trade?       @relation(fields: [tradeId], references: [id], onDelete: Cascade)
  tradeId     String?
  description String?
  logoUrl     String?
  email       String?
  phone       String?
  address     Address?
  memberships Membership[]
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model Property {
  id             String       @id @default(cuid())
  name           String
  imageUrl       String
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  address        Address?
  jobs           Job[]
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}

model Address {
  id             String        @id @default(cuid())
  street         String
  city           String
  state          String
  zip            String
  property       Property?     @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  propertyId     String?       @unique
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?       @unique
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
}

model JobImage {
  id          String   @id @default(cuid())
  url         String
  description String?
  jobId       String
  job         Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Job {
  id         String     @id @default(cuid())
  name       String
  propertyId String
  property   Property   @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  tasks      Task[]
  bids       Bid[]
  images     JobImage[]
  budget     Int
  taskBids   Boolean    @default(false)
  startsAt   DateTime
  deadline   DateTime
  status     JobStatus  @default(DRAFT)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
}

enum JobStatus {
  DRAFT
  PUBLISHED
  CLOSED
  CANCELED
  AWARDED
}

model Task {
  id        String   @id @default(cuid())
  name      String
  jobId     String
  job       Job      @relation(fields: [jobId], references: [id], onDelete: Cascade)
  bids      Bid[]
  trade     Trade    @relation(fields: [tradeId], references: [id], onDelete: Cascade)
  tradeId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Bid {
  id                String       @id @default(cuid())
  name              String
  job               Job          @relation(fields: [jobId], references: [id], onDelete: Cascade)
  jobId             String
  tasks             Task[]
  organization      Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId    String
  amount            Int          @default(0)
  description       String       @default("")
  estimatedDuration Int          @default(7)
  status            BidStatus    @default(PROPOSED)
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  chat              Chat?
}

enum BidStatus {
  PROPOSED
  ACCEPTED
  REJECTED
  CANCELED
}

model Trade {
  id            String         @id @default(cuid())
  name          String         @unique
  organizations Organization[]
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  Task          Task[]
}

model Chat {
  id        String    @id @default(cuid())
  bid       Bid       @relation(fields: [bidId], references: [id], onDelete: Cascade)
  bidId     String    @unique
  messages  Message[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Message {
  id         String   @id @default(cuid())
  content    String
  chat       Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  chatId     String
  senderId   String
  senderType String // "homeowner" or "professional"
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([chatId])
}
