import "server-only";

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "@/db";
import type { Prisma } from "@/db/generated";
import { protectedProcedure, router } from "../trpc";

export const organizationsRouter = router({
  list: protectedProcedure.query(async () => {
    const organizations = await db.organization.findMany({
      orderBy: { name: "asc" },
      include: {
        trade: true,
      },
    });
    return organizations;
  }),
  create: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        tradeId: z.string(),
        description: z.string().optional(),
        email: z.string().email().optional().or(z.literal("")),
        phone: z.string().optional().or(z.literal("")),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const query: Prisma.Args<typeof db.organization, "create">["data"] = {
        name: input.name,
        description: input.description,
        email: input.email || null,
        phone: input.phone || null,
        memberships: {
          create: {
            userId: ctx.auth.userId,
            role: "owner",
          },
        },
      };

      if (input.tradeId) {
        query.trade = {
          connect: { id: input.tradeId },
        };
      }

      const organization = await db.organization.create({ data: query });
      return organization;
    }),
  one: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const organization = await db.organization.findUnique({
        where: { id: input.id },
        include: {
          trade: true,
        },
      });
      return organization;
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        tradeId: z.string(),
        description: z.string().optional(),
        email: z.string().email().optional().or(z.literal("")),
        phone: z.string().optional().or(z.literal("")),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Find organization users and is the userid in the context an applicable role to update?
      const user = await db.membership.findFirst({
        where: {
          organizationId: input.id,
          role: "owner",
          userId: ctx.auth.userId,
        },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      // Update the fields that are updateable on an organization
      const organization = await db.organization.update({
        where: { id: input.id },
        data: {
          name: input.name,
          tradeId: input.tradeId,
          description: input.description,
          email: input.email || null,
          phone: input.phone || null,
        },
      });
      return organization;
    }),
  // Make sure only an organization owner can delete an organization
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Find organization users and is the userid in the context an applicable role to update?
      const user = await db.membership.findFirst({
        where: {
          organizationId: input.id,
          role: "owner",
          userId: ctx.auth.userId,
        },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      const organization = await db.organization.delete({
        where: { id: input.id },
      });
      return organization;
    }),
  firstForUser: protectedProcedure.query(async ({ ctx }) => {
    const organization = await db.organization.findFirst({
      where: { memberships: { some: { userId: ctx.auth.userId } } },
    });
    return organization;
  }),
  listForUser: protectedProcedure.query(async ({ ctx }) => {
    const organizations = await db.organization.findMany({
      where: { memberships: { some: { userId: ctx.auth.userId } } },
    });
    return organizations;
  }),
  getMembers: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const members = await db.membership.findMany({
        where: {
          organizationId: input.organizationId,
        },
        include: {
          organization: true,
        },
      });
      return members;
    }),
});
