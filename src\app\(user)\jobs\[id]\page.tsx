import { currentUser } from "@clerk/nextjs/server";
import { getQueryClient, trpc } from "@/components/trpc/server";
import JobDetailClient from "./client";

export default async function JobDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const user = await currentUser();
  const userType = user?.unsafeMetadata.type as string;

  const queryClient = getQueryClient();
  const job = await queryClient.fetchQuery(trpc.jobs.one.queryOptions({ id }));

  if (!job) {
    throw new Error("Project not found");
  }

  return <JobDetailClient job={job} userType={userType} />;
}
