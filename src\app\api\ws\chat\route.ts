import type { NextRequest } from "next/server";
import { WebSocketServer } from "ws";
import { db } from "@/db";

// Map to store active connections by chatId
const chatConnections = new Map<string, Set<WebSocket>>();

// Initialize WebSocket server (only once)
let wss: WebSocketServer;
if (!wss) {
  wss = new WebSocketServer({ noServer: true });

  wss.on("connection", (ws, req) => {
    const url = new URL(req.url || "", "http://localhost");
    const bidId = url.searchParams.get("bidId");
    const userId = url.searchParams.get("userId");

    if (!bidId || !userId) {
      ws.close(1008, "Missing bidId or userId");
      return;
    }

    // Find or create chat for this bid
    (async () => {
      try {
        let chat = await db.chat.findUnique({
          where: { bidId },
        });

        if (!chat) {
          chat = await db.chat.create({
            data: { bidId },
          });
        }

        const chatId = chat.id;

        // Add connection to the map
        if (!chatConnections.has(chatId)) {
          chatConnections.set(chatId, new Set());
        }
        chatConnections.get(chatId)?.add(ws);

        // Store chatId on the WebSocket instance for cleanup
        (ws as any).chatId = chatId;

        ws.on("message", async (data) => {
          try {
            const messageData = JSON.parse(data.toString());

            // Save message to database
            const savedMessage = await db.message.create({
              data: {
                chatId,
                content: messageData.content,
                senderId: messageData.senderId,
                senderType: messageData.senderType,
              },
            });

            // Broadcast to all connections for this chat
            const connections = chatConnections.get(chatId);
            if (connections) {
              const messageToSend = JSON.stringify(savedMessage);
              connections.forEach((client) => {
                if (client.readyState === WebSocket.OPEN) {
                  client.send(messageToSend);
                }
              });
            }
          } catch (error) {
            console.error("Error processing message:", error);
          }
        });

        ws.on("close", () => {
          // Remove connection when closed
          const chatId = (ws as any).chatId;
          if (chatId) {
            chatConnections.get(chatId)?.delete(ws);
            if (chatConnections.get(chatId)?.size === 0) {
              chatConnections.delete(chatId);
            }
          }
        });
      } catch (error) {
        console.error("Error setting up WebSocket connection:", error);
        ws.close(1011, "Server error");
      }
    })();
  });
}

export function GET(req: NextRequest) {
  const upgrade = req.headers.get("upgrade");
  if (upgrade !== "websocket") {
    return new Response("Expected Upgrade: websocket", { status: 426 });
  }

  // This is a workaround since Next.js doesn't natively support WebSockets yet
  // In production, you might want to use a different approach or a service like Pusher
  return new Response(null, {
    status: 101,
    webSocket: {
      accept: () => {
        // This would be handled by the WebSocketServer
      },
    },
  });
}
