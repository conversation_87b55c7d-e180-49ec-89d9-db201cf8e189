"use client";

import { useUser } from "@clerk/nextjs";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import type { Channel as ChatChannel } from "stream-chat";
import {
  Channel,
  Chat,
  MessageInput,
  MessageList,
  useCreateChatClient,
  Window,
} from "stream-chat-react";
import { useTRPC } from "@/components/trpc/client";
import { env } from "@/env";

import "stream-chat-react/dist/css/v2/index.css";
import "@/styles/stream-chat.css";

export function BidChat({
  bidId,
  userId,
  userToken,
}: {
  bidId: string;
  userId: string;
  userToken: string;
}) {
  const trpc = useTRPC();
  const { user } = useUser();
  const [channel, setChannel] = useState<ChatChannel>();

  // Fetch bid details to get organization IDs
  const { data: bid } = useQuery(trpc.bids.one.queryOptions({ id: bidId }));

  // Fetch members from both organizations
  const { data: bidderMembers } = useQuery(
    trpc.organizations.getMembers.queryOptions(
      { organizationId: bid?.organizationId || "" },
      { enabled: !!bid?.organizationId }
    )
  );

  const { data: propertyOwnerMembers } = useQuery(
    trpc.organizations.getMembers.queryOptions(
      { organizationId: bid?.job?.property?.organizationId || "" },
      { enabled: !!bid?.job?.property?.organizationId }
    )
  );

  const client = useCreateChatClient({
    apiKey: env.NEXT_PUBLIC_STREAM_API_KEY,
    tokenOrProvider: userToken,
    userData: {
      id: userId,
      name: user?.fullName || userId,
      image: user?.imageUrl,
    },
  });

  useEffect(() => {
    if (!client || !bidderMembers || !propertyOwnerMembers || !user) return;

    const setupChannel = async () => {
      try {
        // Connect user with full profile information
        await client.connectUser(
          {
            id: userId,
            name: user.fullName || userId,
            image: user.imageUrl,
          },
          userToken
        );

        // First try to get the existing channel
        const existingChannels = await client.queryChannels({
          id: bidId,
          type: "messaging",
        });

        let channelInstance: ChatChannel;

        if (existingChannels.length > 0) {
          // Channel exists, use the first one
          channelInstance = existingChannels[0] as ChatChannel;
        } else {
          // Create a new channel for this bid
          channelInstance = client.channel("messaging", bidId, {
            members: [
              ...bidderMembers.map((member) => member.userId),
              ...propertyOwnerMembers.map((member) => member.userId),
            ],
          });
        }

        // Initialize the channel
        await channelInstance.watch();
        setChannel(channelInstance);
      } catch (error) {
        console.error("Error setting up chat channel:", error);
      }
    };

    setupChannel();
  }, [
    client,
    bidId,
    bidderMembers,
    propertyOwnerMembers,
    user,
    userToken,
    userId,
  ]);

  if (!client || !channel) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        Loading chat...
      </div>
    );
  }

  return (
    <Chat client={client} theme="stream-chat-custom-theme">
      <Channel channel={channel}>
        <Window>
          <MessageList />
          <MessageInput />
        </Window>
      </Channel>
    </Chat>
  );
}
