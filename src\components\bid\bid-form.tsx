"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useOrganization } from "@/lib/contexts/organization-context";

const bidSchema = z.object({
  name: z.string().min(1, "Bid name is required"),
  amount: z.coerce.number().min(1, "Bid amount is required"),
  description: z.string().min(10, "Please provide a detailed description"),
  estimatedDuration: z.coerce.number().min(1, "Estimated duration is required"),
});

type BidFormValues = z.infer<typeof bidSchema>;

interface BidFormProps {
  jobId: string;
}

export function BidForm({ jobId }: BidFormProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const { organization } = useOrganization();

  const form = useForm<BidFormValues>({
    resolver: zodResolver(bidSchema),
    defaultValues: {
      name: "",
      amount: 0,
      description: "",
      estimatedDuration: 7,
    },
  });

  const createBid = useMutation(
    trpc.bids.create.mutationOptions({
      onSuccess: () => {
        toast.success("Bid submitted successfully");
        router.push("/dashboard");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error submitting bid: ${error.message}`);
      },
    })
  );

  const onSubmit = (data: BidFormValues) => {
    if (!organization?.id) {
      toast.error("You must be part of an organization to submit a bid");
      return;
    }

    createBid.mutate({
      jobId,
      organizationId: organization.id,
      name: data.name,
      amount: data.amount,
      description: data.description,
      estimatedDuration: data.estimatedDuration,
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bid Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter a name for your bid" {...field} />
              </FormControl>
              <FormDescription>
                A clear, concise name for your proposal
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bid Amount ($)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter your bid amount"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                The total amount you're bidding for this project
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="estimatedDuration"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Estimated Duration (days)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter estimated completion time in days"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                How many days you estimate to complete the project
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Bid Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe your approach to this project, including materials, methods, and any special considerations"
                  className="min-h-32"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Provide details about how you'll approach the project
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-orange-600 hover:bg-orange-700"
            disabled={createBid.isPending}
          >
            {createBid.isPending ? "Submitting..." : "Submit Bid"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
