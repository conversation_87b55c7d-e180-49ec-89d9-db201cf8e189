import { allPosts } from "contentlayer/generated";
import { format, parseISO } from "date-fns";

export const generateStaticParams = async () =>
  allPosts.map((post) => ({ slug: post._raw.flattenedPath }));

export const generateMetadata = async ({
  params,
}: {
  params: Promise<{ slug: string }>;
}) => {
  const { slug } = await params;
  const post = allPosts.find((post) => post._raw.flattenedPath === slug);
  if (!post) throw new Error(`Post not found for slug: ${slug}`);
  return {
    title: post.title,
    description: post.description,
  };
};

export default async function PostPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const post = allPosts.find((post) => post._raw.flattenedPath === slug);
  if (!post) throw new Error(`Post not found for slug: ${slug}`);

  return (
    <article className="mx-auto max-w-xl py-8">
      <div className="mb-8 text-center">
        <time dateTime={post.date} className="mb-1 text-gray-600 text-xs">
          {format(parseISO(post.date), "LLLL d, yyyy")}
        </time>
        <h1 className="font-bold text-3xl">{post.title}</h1>
      </div>
      <div
        // biome-ignore lint/security/noDangerouslySetInnerHtml: HTML is from a known source
        dangerouslySetInnerHTML={{ __html: post.body.html }}
        className="[&>*:last-child]:mb-0 [&>*]:mb-3"
      />
    </article>
  );
}
