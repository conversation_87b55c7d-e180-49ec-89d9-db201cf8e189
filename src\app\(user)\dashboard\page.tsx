"use client";

import { useUser } from "@clerk/nextjs";
import { useQuery } from "@tanstack/react-query";
import { ContractorDashboard } from "@/components/contractor/contractor-dashboard";
import { JobsModule } from "@/components/dashboard/jobs-module";
import { Header } from "@/components/header";
import { useTRPC } from "@/components/trpc/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useOrganization } from "@/lib/contexts/organization-context";

export default function DashboardPage() {
  const { user } = useUser();
  const trpc = useTRPC();
  const { organization } = useOrganization();

  // Determine if user is a homeowner or professional
  const userType = user?.unsafeMetadata.type as string;
  const isProfessional = userType === "professional";
  const organizationId = organization?.id;

  // Homeowner data
  const { data: jobs } = useQuery(
    trpc.jobs.listForUser.queryOptions(undefined, {
      enabled: !isProfessional && !!organizationId,
    })
  );

  // Professional data
  const { data: contractorStats } = useQuery(
    trpc.contractor.getStats.queryOptions(
      { organizationId: organizationId || "" },
      {
        enabled: isProfessional && !!organizationId,
      }
    )
  );

  // Calculate homeowner dashboard stats
  const totalJobs = jobs?.length || 0;
  const activeJobs = isProfessional
    ? contractorStats?.activeJobs || 0
    : jobs?.filter((job) => job.status === "PUBLISHED").length || 0;
  const completedJobs = isProfessional
    ? contractorStats?.completedJobs || 0
    : jobs?.filter((job) => job.status === "AWARDED" || job.status === "CLOSED")
        .length || 0;

  return (
    <>
      <Header title={isProfessional ? "Professional Dashboard" : "Dashboard"} />
      <div className="space-y-8 p-8">
        {/* Stats Overview */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardDescription>
                {isProfessional ? "Total Bids" : "Total Jobs"}
              </CardDescription>
              <CardTitle className="text-3xl">
                {isProfessional ? contractorStats?.totalBids || 0 : totalJobs}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-xs">
                {isProfessional
                  ? `Bids submitted by ${
                      organization?.name || "your organization"
                    }`
                  : `Across all ${
                      organization?.name || "your organization"
                    }'s properties`}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Active Jobs</CardDescription>
              <CardTitle className="text-3xl">{activeJobs}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-xs">
                {isProfessional
                  ? `Jobs ${
                      organization?.name || "your organization"
                    } is currently working on`
                  : "Currently published and accepting bids"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardDescription>Completed Jobs</CardDescription>
              <CardTitle className="text-3xl">{completedJobs}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground text-xs">
                {isProfessional
                  ? "Successfully completed projects"
                  : "Successfully awarded or closed"}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Content based on user type */}
        {isProfessional ? <ContractorDashboard /> : <JobsModule />}
      </div>
    </>
  );
}
