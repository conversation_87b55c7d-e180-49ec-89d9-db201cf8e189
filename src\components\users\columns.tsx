"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import { ToggleAdmin } from "../toggle-admin";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";

export type User = {
  id: string;
  name: string;
  email: string;
  isAdmin: boolean;
  avatar: string;
};

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => {
      const user = row.original;

      return (
        <div className="flex items-center">
          <Avatar>
            <AvatarImage src={user.avatar} />
            <AvatarFallback>TC</AvatarFallback>
          </Avatar>
          <span className="ml-2">{user.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "email",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Email
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    id: "admin",
    cell: ({ row }) => {
      const user = row.original;
      return <ToggleAdmin id={user.id} isAdmin={user.isAdmin} />;
    },
  },
];
