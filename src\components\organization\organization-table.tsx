"use client";

import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { useTRPC } from "../trpc/client";
import { buttonVariants } from "../ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";

export function OrganizationTable() {
  const trpc = useTRPC();
  const { data: organizations } = useQuery(
    trpc.organizations.list.queryOptions()
  );

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Trade</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {organizations?.map((organization) => (
          <TableRow key={organization.id} className="border-b">
            <TableCell>{organization.name}</TableCell>
            <TableCell className="flex items-center gap-2">
              <div>{organization.trade?.name}</div>
            </TableCell>
            <TableCell className="text-right">
              <Link
                href={`/organizations/${organization.id}/edit`}
                className="mr-4 text-blue-500 hover:text-blue-600"
              >
                Edit
              </Link>
              <Link
                href={`/organizations/${organization.id}/delete`}
                className={buttonVariants({
                  variant: "destructive",
                })}
              >
                Delete
              </Link>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
