{"models": {"Account": {"id": "Account", "tableName": "Account", "fields": [{"id": "Account.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Account.userId", "name": "userId", "columnName": "userId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.type", "name": "type", "columnName": "type", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.provider", "name": "provider", "columnName": "provider", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.providerAccountId", "name": "providerAccountId", "columnName": "providerAccountId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.refresh_token", "name": "refresh_token", "columnName": "refresh_token", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.access_token", "name": "access_token", "columnName": "access_token", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.expires_at", "name": "expires_at", "columnName": "expires_at", "type": "integer", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.token_type", "name": "token_type", "columnName": "token_type", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.scope", "name": "scope", "columnName": "scope", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.id_token", "name": "id_token", "columnName": "id_token", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.session_state", "name": "session_state", "columnName": "session_state", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Account.refresh_token_expires_in", "name": "refresh_token_expires_in", "columnName": "refresh_token_expires_in", "type": "integer", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "User", "type": "User", "isRequired": true, "kind": "object", "relationName": "AccountToUser", "relationFromFields": ["userId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Account_provider_providerAccountId_key", "fields": ["provider", "providerAccountId"]}, {"name": "Account_pkey", "fields": ["id"]}]}, "Address": {"id": "Address", "tableName": "Address", "fields": [{"id": "Address.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Address.street", "name": "street", "columnName": "street", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Address.city", "name": "city", "columnName": "city", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Address.state", "name": "state", "columnName": "state", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Address.zip", "name": "zip", "columnName": "zip", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Address.propertyId", "name": "propertyId", "columnName": "propertyId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Address.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Address.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Property", "type": "Property", "isRequired": true, "kind": "object", "relationName": "AddressToProperty", "relationFromFields": ["propertyId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Address_propertyId_key", "fields": ["propertyId"]}, {"name": "Address_pkey", "fields": ["id"]}]}, "Bid": {"id": "Bid", "tableName": "Bid", "fields": [{"id": "Bid.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Bid.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Bid.jobId", "name": "jobId", "columnName": "jobId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Bid.organizationId", "name": "organizationId", "columnName": "organizationId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Bid.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Bid.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Organization", "type": "Organization", "isRequired": true, "kind": "object", "relationName": "BidToOrganization", "relationFromFields": ["organizationId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Job", "type": "Job", "isRequired": true, "kind": "object", "relationName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relationFromFields": ["jobId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "_BidToTask", "type": "_BidToTask", "isRequired": false, "kind": "object", "relationName": "_BidToTaskToBid", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Bid_pkey", "fields": ["id"]}]}, "Invitation": {"id": "Invitation", "tableName": "Invitation", "fields": [{"id": "Invitation.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Invitation.email", "name": "email", "columnName": "email", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Invitation.organizationId", "name": "organizationId", "columnName": "organizationId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Invitation.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Invitation.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Invitation.token", "name": "token", "columnName": "token", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Invitation.expiresAt", "name": "expiresAt", "columnName": "expiresAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Invitation.acceptedAt", "name": "acceptedAt", "columnName": "acceptedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Organization", "type": "Organization", "isRequired": true, "kind": "object", "relationName": "InvitationToOrganization", "relationFromFields": ["organizationId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Invitation_pkey", "fields": ["id"]}]}, "Job": {"id": "Job", "tableName": "Job", "fields": [{"id": "Job.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Job.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Job.propertyId", "name": "propertyId", "columnName": "propertyId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Job.budget", "name": "budget", "columnName": "budget", "type": "integer", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Job.taskBids", "name": "taskBids", "columnName": "taskBids", "type": "boolean", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Job.startsAt", "name": "startsAt", "columnName": "startsAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Job.deadline", "name": "deadline", "columnName": "deadline", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Job.draft", "name": "draft", "columnName": "draft", "type": "boolean", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Job.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Job.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Property", "type": "Property", "isRequired": true, "kind": "object", "relationName": "JobToProperty", "relationFromFields": ["propertyId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Bid", "type": "Bid", "isRequired": false, "kind": "object", "relationName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Task", "type": "Task", "isRequired": false, "kind": "object", "relationName": "TaskToJob", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Job_pkey", "fields": ["id"]}]}, "Membership": {"id": "Membership", "tableName": "Membership", "fields": [{"id": "Membership.userId", "name": "userId", "columnName": "userId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Membership.organizationId", "name": "organizationId", "columnName": "organizationId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Membership.role", "name": "role", "columnName": "role", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Organization", "type": "Organization", "isRequired": true, "kind": "object", "relationName": "MembershipToOrganization", "relationFromFields": ["organizationId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "User", "type": "User", "isRequired": true, "kind": "object", "relationName": "MembershipToUser", "relationFromFields": ["userId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Membership_userId_organizationId_key", "fields": ["userId", "organizationId"]}, {"name": "Membership_pkey", "fields": ["userId", "organizationId"]}]}, "Organization": {"id": "Organization", "tableName": "Organization", "fields": [{"id": "Organization.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Organization.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Organization.tradeId", "name": "tradeId", "columnName": "tradeId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Organization.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Organization.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Trade", "type": "Trade", "isRequired": true, "kind": "object", "relationName": "OrganizationToTrade", "relationFromFields": ["tradeId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Bid", "type": "Bid", "isRequired": false, "kind": "object", "relationName": "BidToOrganization", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Invitation", "type": "Invitation", "isRequired": false, "kind": "object", "relationName": "InvitationToOrganization", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Membership", "type": "Membership", "isRequired": false, "kind": "object", "relationName": "MembershipToOrganization", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Property", "type": "Property", "isRequired": false, "kind": "object", "relationName": "PropertyToOrganization", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Organization_pkey", "fields": ["id"]}]}, "Property": {"id": "Property", "tableName": "Property", "fields": [{"id": "Property.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Property.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Property.organizationId", "name": "organizationId", "columnName": "organizationId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Property.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Property.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Organization", "type": "Organization", "isRequired": true, "kind": "object", "relationName": "PropertyToOrganization", "relationFromFields": ["organizationId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Address", "type": "Address", "isRequired": false, "kind": "object", "relationName": "AddressToProperty", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Job", "type": "Job", "isRequired": false, "kind": "object", "relationName": "JobToProperty", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Property_pkey", "fields": ["id"]}]}, "Session": {"id": "Session", "tableName": "Session", "fields": [{"id": "Session.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Session.sessionToken", "name": "sessionToken", "columnName": "sessionToken", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Session.userId", "name": "userId", "columnName": "userId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Session.expires", "name": "expires", "columnName": "expires", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "User", "type": "User", "isRequired": true, "kind": "object", "relationName": "SessionToUser", "relationFromFields": ["userId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Session_sessionToken_key", "fields": ["sessionToken"]}, {"name": "Session_pkey", "fields": ["id"]}]}, "Task": {"id": "Task", "tableName": "Task", "fields": [{"id": "Task.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Task.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Task.jobId", "name": "jobId", "columnName": "jobId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Task.tradeId", "name": "tradeId", "columnName": "tradeId", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Task.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Task.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Trade", "type": "Trade", "isRequired": true, "kind": "object", "relationName": "TaskToTrade", "relationFromFields": ["tradeId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Job", "type": "Job", "isRequired": true, "kind": "object", "relationName": "TaskToJob", "relationFromFields": ["jobId"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "_BidToTask", "type": "_BidToTask", "isRequired": false, "kind": "object", "relationName": "_BidToTaskToTask", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Task_pkey", "fields": ["id"]}]}, "Trade": {"id": "Trade", "tableName": "Trade", "fields": [{"id": "Trade.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "Trade.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "Trade.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "Trade.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Organization", "type": "Organization", "isRequired": false, "kind": "object", "relationName": "OrganizationToTrade", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Task", "type": "Task", "isRequired": false, "kind": "object", "relationName": "TaskToTrade", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "Trade_name_key", "fields": ["name"]}, {"name": "Trade_pkey", "fields": ["id"]}]}, "User": {"id": "User", "tableName": "User", "fields": [{"id": "User.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "User.name", "name": "name", "columnName": "name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "User.email", "name": "email", "columnName": "email", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "User.emailVerified", "name": "emailVerified", "columnName": "emailVerified", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "User.image", "name": "image", "columnName": "image", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "User.createdAt", "name": "createdAt", "columnName": "createdAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "User.updatedAt", "name": "updatedAt", "columnName": "updatedAt", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Account", "type": "Account", "isRequired": false, "kind": "object", "relationName": "AccountToUser", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Membership", "type": "Membership", "isRequired": false, "kind": "object", "relationName": "MembershipToUser", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Session", "type": "Session", "isRequired": false, "kind": "object", "relationName": "SessionToUser", "relationFromFields": [], "relationToFields": [], "isList": true, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "User_email_key", "fields": ["email"]}, {"name": "User_pkey", "fields": ["id"]}]}, "VerificationToken": {"id": "VerificationToken", "tableName": "VerificationToken", "fields": [{"id": "VerificationToken.identifier", "name": "identifier", "columnName": "identifier", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "VerificationToken.token", "name": "token", "columnName": "token", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "VerificationToken.expires", "name": "expires", "columnName": "expires", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}], "uniqueConstraints": [{"name": "VerificationToken_identifier_token_key", "fields": ["identifier", "token"]}, {"name": "VerificationToken_token_key", "fields": ["token"]}]}, "_BidToTask": {"id": "_BidToTask", "tableName": "_BidToTask", "fields": [{"id": "_BidToTask.A", "name": "A", "columnName": "A", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "_BidToTask.B", "name": "B", "columnName": "B", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"name": "Task", "type": "Task", "isRequired": true, "kind": "object", "relationName": "_BidToTaskToTask", "relationFromFields": ["B"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}, {"name": "Bid", "type": "Bid", "isRequired": true, "kind": "object", "relationName": "_BidToTaskToBid", "relationFromFields": ["A"], "relationToFields": ["id"], "isList": false, "isId": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false}], "uniqueConstraints": [{"name": "_BidToTask_A_B_key", "fields": ["A", "B"]}]}, "_prisma_migrations": {"id": "_prisma_migrations", "tableName": "_prisma_migrations", "fields": [{"id": "_prisma_migrations.id", "name": "id", "columnName": "id", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": true}, {"id": "_prisma_migrations.checksum", "name": "checksum", "columnName": "checksum", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "_prisma_migrations.finished_at", "name": "finished_at", "columnName": "finished_at", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "_prisma_migrations.migration_name", "name": "migration_name", "columnName": "migration_name", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "_prisma_migrations.logs", "name": "logs", "columnName": "logs", "type": "text", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "_prisma_migrations.rolled_back_at", "name": "rolled_back_at", "columnName": "rolled_back_at", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": false, "isId": false}, {"id": "_prisma_migrations.started_at", "name": "started_at", "columnName": "started_at", "type": "datetime", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}, {"id": "_prisma_migrations.applied_steps_count", "name": "applied_steps_count", "columnName": "applied_steps_count", "type": "integer", "isRequired": true, "kind": "scalar", "isList": false, "isGenerated": false, "sequence": false, "hasDefaultValue": true, "isId": false}], "uniqueConstraints": [{"name": "_prisma_migrations_pkey", "fields": ["id"]}]}}, "enums": {}}