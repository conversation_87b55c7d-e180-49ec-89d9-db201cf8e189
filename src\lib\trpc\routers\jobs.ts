import "server-only";

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "@/db";
import type { Prisma } from "@/db/generated";
import { protectedProcedure, router } from "../trpc";

export const jobsRouter = router({
  list: protectedProcedure.query(async () => {
    const jobs = await db.job.findMany({
      orderBy: { name: "asc" },
    });
    return jobs;
  }),
  create: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        budget: z.number(),
        tasks: z.object({ name: z.string(), tradeId: z.string() }).array(),
        propertyId: z.string(),
        startsAt: z.date(),
        deadline: z.date(),
        images: z
          .object({ url: z.string(), description: z.string() })
          .array()
          .optional(),
      }),
    ) // z is from Zod ( validation )
    .mutation(async ({ input, ctx }) => {
      const query: Prisma.Args<typeof db.job, "create">["data"] = {
        name: input.name,
        budget: input.budget,
        propertyId: input.propertyId,
        startsAt: input.startsAt,
        deadline: input.deadline,
        tasks: {
          create: input.tasks.map((task) => ({
            name: task.name,
            tradeId: task.tradeId,
          })),
        },
      };

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        query.images = {
          create: input.images.map((image) => ({
            url: image.url,
            description: image.description,
          })),
        };
      }

      const job = await db.job.create({ data: query });
      return job;
    }),
  one: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const job = await db.job.findUnique({
        where: { id: input.id },
        include: {
          property: true,
          tasks: true,
          bids: true,
          images: true,
        },
      });
      return job;
    }),
  update: protectedProcedure // what part of org can we change?  how do we know the user who owns the org?
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        propertyId: z.string(),
        images: z
          .object({ url: z.string(), description: z.string() })
          .array()
          .optional(),
      }),
    ) // z is from Zod ( validation )
    .mutation(async ({ input, ctx }) => {
      // Find job users and is the userid in the context an applicable role to update?
      const user = await db.membership.findFirst({
        where: {
          role: "owner",
          userId: ctx.auth.userId,
        },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      // First, delete existing images if we're updating them
      if (input.images) {
        await db.jobImage.deleteMany({
          where: { jobId: input.id },
        });
      }

      // Update the fields that are updateable on a job
      const updateData: Prisma.JobUpdateInput = {
        name: input.name,
        property: {
          connect: { id: input.propertyId },
        },
      };

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        updateData.images = {
          create: input.images.map((image) => ({
            url: image.url,
            description: image.description,
          })),
        };
      }

      const job = await db.job.update({
        where: { id: input.id },
        data: updateData,
      });
      return job;
    }),
  // Make sure only an job owner can delete an job
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Find job users and is the userid in the context an applicable role to update?
      const user = await db.membership.findFirst({
        where: {
          role: "owner",
          userId: ctx.auth.userId,
        },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      const job = await db.job.delete({
        where: { id: input.id },
      });
      return job;
    }),
  // firstForUser: protectedProcedure.query(async ({ ctx }) => {

  //     const job = await db.job.findFirst({
  //         where: { memberships: { some: { userId: ctx.auth.userId } } },
  //     });
  //     return job;
  // }),

  listForUser: protectedProcedure.query(async ({ input, ctx }) => {
    // Find job users and is the userid in the context an applicable role to update?

    const jobs = await db.job.findMany({
      where: {
        property: {
          organization: {
            memberships: {
              some: {
                userId: ctx.auth.userId,
              },
            },
          },
        },
      },
      include: {
        property: true,
      },
    });
    return jobs;
  }),
  listPublished: protectedProcedure.query(async () => {
    const jobs = await db.job.findMany({
      where: {
        status: "PUBLISHED",
      },
      include: {
        property: {
          include: {
            organization: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return jobs;
  }),
  listActiveForOrganization: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const jobs = await db.job.findMany({
        where: {
          bids: {
            some: {
              organizationId: input.organizationId,
              status: "ACCEPTED",
            },
          },
          status: "PUBLISHED",
        },
        include: {
          property: {
            include: {
              organization: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return jobs;
    }),
  publish: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Find job users and is the userid in the context an applicable role to update?
      const user = await db.membership.findFirst({
        where: {
          role: "owner",
          userId: ctx.auth.userId,
        },
      });

      if (!user) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      // Update the job status to PUBLISHED
      const job = await db.job.update({
        where: { id: input.id },
        data: {
          status: "PUBLISHED",
        },
      });
      return job;
    }),
});
