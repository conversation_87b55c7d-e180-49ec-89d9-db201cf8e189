import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { BidForm } from "@/components/bid/bid-form";
import { Header } from "@/components/header";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default async function BidJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const user = await currentUser();
  const { id } = await params;

  // Redirect if not logged in
  if (!user) {
    redirect("/sign-in");
  }

  // Redirect if not a professional
  const userType = user.unsafeMetadata.type as string;
  if (userType !== "professional") {
    redirect("/dashboard");
  }

  const queryClient = getQueryClient();
  const job = await queryClient.fetchQuery(trpc.jobs.one.queryOptions({ id }));

  // Redirect if job not found or not published
  if (!job || job.status !== "PUBLISHED") {
    redirect("/dashboard");
  }

  return (
    <>
      <Header title={`Submit Bid: ${job.name}`} />
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Submit Your Bid</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <h3 className="font-medium">Project Details</h3>
                <p className="text-muted-foreground">
                  Property: {job.property.name}
                </p>
                <p className="text-muted-foreground">Budget: ${job.budget}</p>
              </div>
              <BidForm jobId={id} />
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
