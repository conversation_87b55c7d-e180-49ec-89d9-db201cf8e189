import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

type JobStatus = "DRAFT" | "PUBLISHED" | "CLOSED" | "CANCELED" | "AWARDED";
type BidStatus = "PROPOSED" | "ACCEPTED" | "REJECTED" | "CANCELED";

export function getJobStatusVariant(status: JobStatus) {
  switch (status) {
    case "AWARDED":
      return "success";
    case "PUBLISHED":
      return "default";
    case "CANCELED":
      return "destructive";
    case "CLOSED":
      return "secondary";
    default:
      return "outline";
  }
}

export function getBidStatusVariant(status: BidStatus) {
  switch (status) {
    case "ACCEPTED":
      return "success";
    case "REJECTED":
    case "CANCELED":
      return "destructive";
    default:
      return "default";
  }
}
