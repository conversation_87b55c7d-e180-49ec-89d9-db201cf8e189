"use client";

import { CheckIcon, ChevronsUpDownIcon, PlusIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import type { Organization } from "@/db/generated";
import { useOrganization } from "@/lib/contexts/organization-context";
import logo from "./tc-logomark.webp";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "./ui/sidebar";

export function OrgSwitcher() {
  const {
    organization: currentOrganization,
    setOrganization,
    organizationList,
  } = useOrganization();

  const getOrganization = (org: Organization) => {
    setOrganization(org);
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton size={"lg"}>
              <Image alt="TradeCrews" src={logo} width={40} />
              <span className="truncate font-semibold">
                {currentOrganization?.name}
              </span>
              <ChevronsUpDownIcon className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side="right"
            className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-56 rounded-lg"
            align="start"
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Organizations
            </DropdownMenuLabel>
            {organizationList?.map((organization) => (
              <DropdownMenuItem
                key={organization.id}
                onClick={() => getOrganization(organization)}
                className="gap-2 p-2"
              >
                {currentOrganization?.id === organization.id && (
                  <CheckIcon className="size-4" />
                )}
                {organization.name}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2 p-2" asChild>
              <Link
                href="/organizations/new"
                className="font-medium text-muted-foreground"
              >
                <div className="flex size-6 items-center justify-center rounded-md border bg-background">
                  <PlusIcon className="size-4" />
                </div>
                Add Organization
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
