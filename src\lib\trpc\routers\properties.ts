import "server-only";

import { z } from "zod";
import { db } from "@/db";
import { protectedProcedure, router } from "../trpc";

export const propertiesRouter = router({
  list: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      return await db.property.findMany({
        where: { organizationId: input.organizationId },
        orderBy: { name: "asc" },
        include: {
          address: true,
        },
      });
    }),
  create: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        imageUrl: z.string(),
        organizationId: z.string(),
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          zip: z.string(),
        }),
      }),
    )
    .mutation(async ({ input }) => {
      return await db.property.create({
        data: {
          name: input.name,
          imageUrl: input.imageUrl,
          organization: {
            connect: { id: input.organizationId },
          },
          address: {
            create: {
              street: input.address.street,
              city: input.address.city,
              state: input.address.state,
              zip: input.address.zip,
            },
          },
        },
      });
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        organizationId: z.string(),
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          zip: z.string(),
        }),
      }),
    )
    .mutation(async ({ input }) => {
      return await db.property.update({
        where: { id: input.id },
        data: {
          name: input.name,
          organizationId: input.organizationId,
          address: {
            update: {
              street: input.address.street,
              city: input.address.city,
              state: input.address.state,
              zip: input.address.zip,
            },
          },
        },
      });
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      return await db.property.delete({
        where: { id: input.id },
      });
    }),
});
