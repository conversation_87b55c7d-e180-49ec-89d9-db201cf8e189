import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { PostHogProvider } from "@/components/posthog-provider";
import { ThemeProvider } from "@/components/theme-provider";
import { TRPCReactProvider } from "@/components/trpc/client";
import { Toaster } from "@/components/ui/sonner";

import "@/styles/globals.css";

export const metadata: Metadata = {
  title: "TradeCrews",
  description: "Connecting homeowners with the trades",
  appleWebApp: { title: "TradeCrews" },
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <ClerkProvider waitlistUrl="/">
      <html
        lang="en"
        className={`${geist.variable} h-full`}
        suppressHydrationWarning
      >
        <head />
        <body className="h-full">
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <PostHogProvider>
              <TRPCReactProvider>
                {children}
                <Toaster />
              </TRPCReactProvider>
            </PostHogProvider>
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
