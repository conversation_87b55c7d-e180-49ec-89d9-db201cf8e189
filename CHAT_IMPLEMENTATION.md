# Chat Implementation with <PERSON><PERSON><PERSON> and Local Transcripts

This document describes the chat functionality implementation using <PERSON><PERSON><PERSON> for real-time communication while maintaining local transcripts in the database.

## Overview

The chat system provides real-time messaging between homeowners and contractors for each bid, with the following features:

- **Real-time messaging** via Pusher
- **Local transcript storage** in PostgreSQL database
- **Typing indicators** to show when users are typing
- **Online presence** to show who's currently online
- **Message persistence** for chat history
- **Secure authentication** via Clerk integration

## Architecture

### Database Schema

The chat functionality uses two main models:

```prisma
model Chat {
  id        String    @id @default(cuid())
  bid       Bid       @relation(fields: [bidId], references: [id], onDelete: Cascade)
  bidId     String    @unique
  messages  Message[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Message {
  id         String   @id @default(cuid())
  content    String
  chat       Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  chatId     String
  senderId   String
  senderType String // "homeowner" or "professional"
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([chatId])
}
```

### Components

1. **PusherChat** (`src/components/bid/pusher-chat.tsx`)
   - Main chat component with real-time messaging
   - Handles message display, sending, and real-time updates
   - Shows typing indicators and online presence

2. **Pusher Server Utils** (`src/lib/pusher-server.ts`)
   - Server-side Pusher client configuration
   - Helper functions for triggering events

3. **Pusher Client Utils** (`src/lib/pusher-client.ts`)
   - Client-side Pusher configuration
   - Channel subscription and event handling

### API Endpoints

1. **Authentication** (`src/app/api/pusher/auth/route.ts`)
   - Authenticates users for private Pusher channels
   - Verifies user access to specific chats

2. **Typing Indicators** (`src/app/api/pusher/typing/route.ts`)
   - Handles typing indicator events

3. **Presence Updates** (`src/app/api/pusher/presence/route.ts`)
   - Manages user online/offline status

### TRPC Routes

Enhanced message routes in `src/lib/trpc/routers/messages.ts`:

- `listForBid` - Fetch messages for a specific bid
- `create` - Create new message and trigger Pusher event

## Setup Instructions

### 1. Install Dependencies

```bash
npm install pusher pusher-js
```

### 2. Configure Environment Variables

Add the following to your `.env` file:

```env
# Pusher Configuration
PUSHER_APP_ID="your_app_id"
PUSHER_KEY="your_key"
PUSHER_SECRET="your_secret"
PUSHER_CLUSTER="us2"
NEXT_PUBLIC_PUSHER_KEY="your_key"
NEXT_PUBLIC_PUSHER_CLUSTER="us2"
```

### 3. Create Pusher App

1. Sign up at [pusher.com](https://pusher.com)
2. Create a new app
3. Copy the credentials to your environment variables

### 4. Database Migration

The database schema is already set up with Chat and Message models. If you need to apply migrations:

```bash
npm run db:generate
```

## Usage

### Basic Chat Implementation

```tsx
import { PusherChat } from "@/components/bid/pusher-chat";

function BidPage({ bidId, userId }) {
  return (
    <div>
      {/* Other bid content */}
      <PusherChat bidId={bidId} userId={userId} />
    </div>
  );
}
```

### Features

#### Real-time Messaging
- Messages are stored in the database via TRPC
- Pusher events are triggered for real-time delivery
- All connected users receive messages instantly

#### Typing Indicators
- Shows when other users are typing
- Automatically clears after 3 seconds of inactivity
- Only visible to other users (not the typer)

#### Online Presence
- Shows number of online users
- Updates when users join/leave the chat
- Persists across page refreshes

#### Message Persistence
- All messages are stored in PostgreSQL
- Chat history is preserved
- Messages load from database on page refresh

## Security

### Channel Authentication
- Private channels require authentication
- Users must be members of either the bidding organization or property owner organization
- Authentication handled via Clerk integration

### Access Control
- TRPC routes verify user permissions
- Users can only access chats for bids they're involved in
- Server-side validation for all operations

## Monitoring and Debugging

### Development Mode
- Pusher logging is enabled in development
- Console logs for connection status and errors
- Error boundaries for graceful failure handling

### Production Considerations
- Error handling for Pusher connection failures
- Fallback to database polling if real-time fails
- Rate limiting on message creation

## Troubleshooting

### Common Issues

1. **Pusher Connection Fails**
   - Check environment variables
   - Verify Pusher app configuration
   - Check network connectivity

2. **Messages Not Appearing**
   - Verify TRPC routes are working
   - Check database connection
   - Ensure Pusher events are being triggered

3. **Authentication Errors**
   - Verify Clerk configuration
   - Check user permissions
   - Ensure proper organization membership

### Debug Steps

1. Check browser console for errors
2. Verify Pusher connection in network tab
3. Test TRPC routes independently
4. Check database for message storage

## Future Enhancements

- File/image sharing in chat
- Message reactions and replies
- Push notifications for offline users
- Message search functionality
- Chat moderation tools
