"use client";

import { useQuery } from "@tanstack/react-query";
import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { useTRPC } from "../trpc/client";
import { buttonVariants } from "../ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import TradeOrganizations from "./trade-organizations";

export function TradeTable() {
  const trpc = useTRPC();
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Organizations</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {trades?.map((trade) => (
          <Collapsible key={trade.id} asChild>
            <TableRow className="border-b">
              <TableCell>{trade.name}</TableCell>
              <TableCell className="flex items-center gap-2">
                <div>{trade._count.organizations}</div>
                <CollapsibleTrigger asChild>
                  <ChevronDown />
                </CollapsibleTrigger>
              </TableCell>
              <TableCell className="text-right">
                <Link
                  href={`/admin/trades/${trade.id}/edit`}
                  className="mr-4 text-blue-500 hover:text-blue-600"
                >
                  Edit
                </Link>
                <Link
                  href={`/admin/trades/${trade.id}/delete`}
                  className={buttonVariants({
                    variant: "destructive",
                  })}
                >
                  Delete
                </Link>
              </TableCell>
            </TableRow>
            <CollapsibleContent asChild>
              <TradeOrganizations organizations={trade.organizations} />
            </CollapsibleContent>
          </Collapsible>
        ))}
      </TableBody>
    </Table>
  );
}
