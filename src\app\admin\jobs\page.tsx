"use server";

import Link from "next/link";
import { Header } from "@/components/header";
import { buttonVariants } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { db } from "@/db";

export default async function JobsPage() {
  const jobs = await db.job.findMany({
    orderBy: { name: "asc" },
    include: {
      _count: {
        select: { bids: true },
      },
    },
  });

  return (
    <>
      <Header title="Jobs" />
      <div className="p-8">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Bids</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {jobs.map((job) => (
              <TableRow key={job.id} className="border-b">
                <TableCell>
                  <Link href={`/admin/jobs/${job.id}`}>{job.name}</Link>
                </TableCell>
                <TableCell>{job._count.bids}</TableCell>
                <TableCell>{job.createdAt.toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <Link
                    href={`/admin/jobs/${job.id}/edit`}
                    className="mr-4 text-blue-500 hover:text-blue-600"
                  >
                    Edit
                  </Link>
                  <Link
                    href={`/admin/jobs/${job.id}/delete`}
                    className={buttonVariants({ variant: "destructive" })}
                  >
                    Delete
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </>
  );
}
