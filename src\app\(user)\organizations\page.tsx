import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import Link from "next/link";
import { Head<PERSON> } from "@/components/header";
import { OrganizationTable } from "@/components/organization/organization-table";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { buttonVariants } from "@/components/ui/button";

export default function OrganizationsPage() {
  const queryClient = getQueryClient();
  void queryClient.prefetchQuery(trpc.organizations.list.queryOptions());

  return (
    <>
      <Header title="Organizations">
        <Link
          href="/organizations/new"
          className={buttonVariants({ variant: "default" })}
        >
          Add Organization
        </Link>
      </Header>
      <div className="p-8">
        <HydrationBoundary state={dehydrate(queryClient)}>
          <OrganizationTable />
        </HydrationBoundary>
      </div>
    </>
  );
}
