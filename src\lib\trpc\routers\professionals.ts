import { db } from "@/db";
import { protectedProcedure, router } from "../trpc";

export const professionalsRouter = router({
  getStats: protectedProcedure.query(async ({ ctx }) => {
    const stats = await db.organization.findUnique({
      where: { userId: ctx.auth.userId },
      select: {
        totalBids: true,
        activeJobs: true,
        completedJobs: true,
      },
    });
    return stats;
  }),
});
