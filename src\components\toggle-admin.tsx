"use client";

import { useMutation } from "@tanstack/react-query";
import { ShieldIcon } from "lucide-react";
import { useTRPC } from "./trpc/client";
import { Toggle } from "./ui/toggle";

export function ToggleAdmin({
  id,
  isAdmin,
}: Readonly<{ id: string; isAdmin?: boolean }>) {
  const trpc = useTRPC();
  const setAdmin = useMutation(trpc.users.setAdmin.mutationOptions());

  const changeAdmin = (pressed: boolean) => {
    setAdmin.mutate({ id, admin: pressed });
  };

  return (
    <Toggle onPressedChange={changeAdmin} pressed={isAdmin}>
      <ShieldIcon className="h-4 w-4" />
    </Toggle>
  );
}
