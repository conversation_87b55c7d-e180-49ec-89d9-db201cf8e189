import { PrismaClient } from "@prisma/client";
import Link from "next/link";
import { Head<PERSON> } from "@/components/header";
import { buttonVariants } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { db } from "@/db";

export default async function JobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const job = await db.job.findUnique({
    where: {
      id: id,
    },

    include: {
      property: true,
      tasks: true,
      bids: true,
    },
  });
  return (
    <main className="p-8">
      <h1>
        <strong>Job:</strong> {job?.name}
      </h1>
      <h2>
        <strong>Property:</strong> {job?.property.name}
      </h2>
      <h2>
        <strong>Bids</strong>
      </h2>
      <ul>
        {job?.bids.map((bid, index) => (
          <li key={bid.id}>
            <h3>
              {index + 1}. {bid.name}
            </h3>
            <p>Created: {bid.createdAt.toLocaleDateString()}</p>
            <p>Updated: {bid.createdAt.toLocaleDateString()}</p>
          </li>
        ))}
      </ul>
      <h2>
        <strong>Tasks</strong>
      </h2>
      <ul>
        {job?.tasks.map((task, index) => (
          <li key={task.id}>
            <h3>
              {index + 1}. {task.name}
            </h3>
            <p>Created: {task.createdAt.toLocaleDateString()}</p>
            <p>Updated: {task.createdAt.toLocaleDateString()}</p>
          </li>
        ))}
      </ul>
    </main>
  );
}
