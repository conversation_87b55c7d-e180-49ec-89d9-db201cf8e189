[{"input": "account id", "examples": ["AC12345", "AB67890", "US-1234", "GB-9876", "EU-12345", "CN-67890", "IN-54321", "FR-76543", "DE-54321", "IT-98765", "CA-12345", "AU-67890", "RU-54321", "JP-76543", "ES-98765", "SE-12345", "CH-67890", "NL-54321", "PL-76543", "TR-98765"], "description": "The unique identifier of a user's account in the system"}, {"input": "account type", "examples": ["USER", "ADMIN", "MODERATOR", "OWNER", "CUSTOMER", "GUEST", "EMPLOYEE", "MANAGER", "ADMINISTRATOR", "GENERAL_USER", "VIP_MEMBER", "SUBSCRIBER", "REGISTERED_USER", "MEMBER", "SUPER_ADMIN", "USER_MANAGER", "GOLD_MEMBER", "SILVER_MEMBER", "BRONZE_MEMBER", "PREMIUM_MEMBER"], "description": "Represents the type of account associated with the user, such as 'USER' or 'ADMIN'."}, {"input": "account provider", "examples": ["Google", "PayPal", "Amazon Web Services", "Facebook", "Stripe", "Apple", "Microsoft", "Amazon", "Etsy", "Square", "Shopify", "Shopify Payments", "Gumroad", "<PERSON><PERSON><PERSON>", "ClickBank", "PayPal Payments Standard", "Stripe Connect", "WePay", "Authorize.net", "Braintree"], "description": "The provider account information associated with the user's account"}, {"input": "account provider_account_id", "examples": ["12345", "ABCD-1234", "ACME-5678", "PROV-123", "ABC123", "**********", "PROVIDER-1234", "123-ABC-DEF", "XYZ-1234", "1234-5678-9012", "ABC1234", "1234-56789", "PROVIDER-12345", "123456-7890", "ABC-1234-5678", "123-4567-8901", "PROV-123456", "12345-6789", "ABC-12345-6789", "1234567-8901"], "description": "A unique identifier for the account associated with the provider."}, {"input": "account refresh_token", "examples": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3OTQzIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5NjUyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3OTUyIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5NjUyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3OTUzIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5NjUxfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3OTU0IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5NjU0fQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3OTU1IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5NjUxfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3OTU2IiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5NjU0fQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"], "description": "The refresh token issued by the authorization server, used to obtain a new access token when the current access token expires."}, {"input": "account access_token", "examples": ["vHb2sI6WvBZbG9p2uG9rH1q6p5b1uG2p5r1t4s", "9p8o7n6m5l4k3j2i1h0gG3f2e1d", "9e7d6c5b4a3b2a1zYxwv", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************.gpRqQ1lN6iKcF7KZyWZ6R9P6L9Dw2z3g1yB6xYB", "A1B2C3D4E5F6G7H8I9J0K1L2M3", "PZ9E5T7D2A1G6J8K4C9B2N5M0", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.ZKJkz1I6vZ1I6vZ1I5p5p6uH7b2uG9p2uG9rH1q6", "Q1W2E3R4T5Y6U7I8O9P0S1N2M1", "vP9o7n6m5l4k3j2i1h0gF3e2d1c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.gpRqQ1lN6iKcF7KZyWZ6R9P6L9Dw2z3g1yB6xYB", "9s0d9c9b8a7a6l5k4j3i2h1gF4E", "A1B2C3D4E5F6G7H8I9J0K1L2M3", "9p8o7n6m5l4k3j2i1h0gG3f2e1d", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "PZ9E5T7D2A1G6J8K4C9B2N5M0", "vP9o7n6m5l4k3j2i1h0gF3e2d1c", "9s0d9c9b8a7a6l5k4j3i2h1gF4E", "9e7d6c5b4a3b2a1zYxwv"], "description": "An access token used for authentication purposes, allowing access to user's or provider's account in the real estate or auction management platform."}, {"input": "account token_type", "examples": ["Bearer", "Refresh", "Bearer", "Session", "Refresh", "Bearer", "Access", "Bearer", "Refresh", "OAuth", "Bearer", "Token", "Bearer", "Refresh", "JWT", "Bearer", "Session", "Bearer", "OAuth", "Bearer"], "description": "The type of token (e.g. <PERSON><PERSON>, Refresh)"}, {"input": "account scope", "examples": ["user_profile_management", "property_listing", "bidding", "payment_processing", "listing_management", "auction_management", "payment_gateway", "user_registration", "user_login", "user_profile_editing", "listing_creation", "listing_editing", "listing_deletion", "bid_management", "auction_creation", "auction_editing", "auction_closing", "reporting", "admin_dashboard", "site_settings"], "description": "The set of permissions or access rights granted to the user's account, typically including the types of actions or resources that can be accessed within the platform, such as user profile management, property listing, bidding, or payment processing."}, {"input": "account id_token", "examples": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwibmFtZSI6IkpvaG4gRG9lIiwic3RhZ2UiOiIxNDIwMDAwIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"], "description": "An opaque token issued to the client by the authorization server. The contents of the token can vary depending on the authorization server, but it typically contains information about the user and the client."}, {"input": "account session_state", "examples": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaGFuIjoiMjMwfQ.5JFJ2a1n5SgTq9mJ4Ld6S1Tjg0g4N6zZ"], "description": "This column is a JSON Web Token (JWT) that represents the user's session information."}, {"input": "address id", "examples": ["AD123", "ADDR-001", "A001", "ID-1234", "ADDR-2", "A-001", "ADDR001", "ADDR-3", "ID-001", "ADDR-4", "A-002", "ADDR-5", "ID-002", "ADDR-6", "A-003", "ADDR-7", "ID-003", "ADDR-8", "A-004", "ADDR-9"], "description": "Unique identifier for a specific address record in the database"}, {"input": "address street", "examples": ["123 Main St", "456 Elm St Apt 101", "789 Oak St #202", "987 Maple St", "111 Center St Suite 300", "222 Broadway", "333 Park Ave", "444 Oak St Ste 1", "555 Park St", "666 Main St Unit 3", "777 Elm St", "888 Center St", "999 Broadway #4", "1011 Oak St Apt 205", "1111 Maple St Ste 2", "1212 Park Ave #1", "1313 Main St", "1414 Elm St", "1515 Center St", "1616 Broadway"], "description": "Physical street address of a property or location"}, {"input": "address city", "examples": ["New York", "Los Angeles", "Chicago", "Houston", "Philadelphia", "Phoenix", "San Antonio", "San Diego", "Dallas", "San Jose", "Austin", "Jacksonville", "San Francisco", "Indianapolis", "Columbus", "Fort Worth", "Charlotte", "Memphis", "Boston", "Baltimore"], "description": "The city where a property is located, used for geolocation and address validation."}, {"input": "address state", "examples": ["Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland"], "description": "The United States state (e.g., California, New York, Florida) for the property's address"}, {"input": "address zip", "examples": ["10001", "02138", "44444", "12345", "90210", "78701", "99501", "32001", "10075", "85001", "40202", "63101", "77002", "33602", "70001", "46001", "30001", "57001", "27101", "87501"], "description": "A 5-digit numeric code representing the United States postal code for the property's location"}, {"input": "bid id", "examples": ["b0d85f2e", "4d3c4f6e", "dd78c2a9", "a1e6c5e2", "7be4c6f3", "c9a8e5d4", "f7d6c5b0", "b8a7c6d5", "e5d4c3b2", "9c8b6a5d", "c6d5c4b8", "a9b8c7d6", "d5c4b3a7", "e7f6d5c4", "c2a9b8c7", "d4b3a7c6", "f5e4d3c2", "8c7b6a5d", "3b2a7c6d", "e6d5c4b8", "a7c6d5c4"], "description": "A unique identifier for each bid in the system."}, {"input": "bid name", "examples": ["<PERSON>", "<PERSON>", "Alpha Corporation", "Beta Inc.", "Charlie Trading", "Delta Investments", "Epsilon Ventures", "Gamma Partners", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "ABC Services", "XYZ Solutions", "Pacific Builders", "Ocean View Properties", "Green Earth Developers", "Sky Blue Construction", "International Trading Co.", "New York Holdings"], "description": "Displays the name associated with a bid, likely the name of the bidder or the winning bidder."}, {"input": "invitation id", "examples": ["INV001", "AUC-2023-001", "REF-INV-1234", "INV-123", "A-456", "INV-A123", "INV-009", "R-INV-001", "INV-4567", "INV-89", "A-INV-001", "INV-A-123", "INV-1001", "INV-0012", "INV-456", "INV-7890", "INV-12345", "INV-6789", "INV-INV-001", "INV-0987"], "description": "Unique identifier for an invitation in the real estate or auction management platform."}, {"input": "invitation email", "examples": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "description": "Email address associated with an invitation sent to a user, likely for registration or bidding purposes in a real estate or auction management platform."}, {"input": "invitation token", "examples": ["UWYRQF4QZ9", "LYR6M4Q7X8", "2A3E6B7C9D", "4F5G6H7J8K9", "ZXCVBNMKBHGF", "DFGHJKBHGFCD", "LKJHGFCDXCVB", "BHGFCVXZASDF", "ASDFGHJKLPOIU", "POIUUYTREWQAZ", "QWERTYUIOPASDF", "LKJHGFCDASDFGH", "ZXCVBNMKBHGFCD", "2A3E6B7C9D0F5G", "LYR6M4Q7X8UWYRQ", "UWYRQF4QZ9LYR6M", "4F5G6H7J8K9ZXCV", "DFGHJKBHGFCDASD", "BHGFCVXZASDFGHJ", "ASDFGHJKLPOIUUYT"], "description": "A unique verification token sent to users via email when they receive an invitation to join the platform."}, {"input": "job id", "examples": ["J-101", "JD1234", "J-001", "J-202", "JOBNUM-001", "JD4567", "J-303", "JOBNUM-002", "J-404", "JD-008", "JOBNUM-003", "J-505", "JD9012", "JOBNUM-004", "J-606", "JD-015", "JOBNUM-005", "J-707", "JD-022", "JOBNUM-006", "J-808"], "description": "Unique identifier for each job listing in the system."}, {"input": "job name", "examples": ["Part-time Customer Service Representative Needed", "Luxury 3 Bedroom House for Rent in Downtown Area", "Software Engineer Position Open for Immediate Hire", "Seeking Experienced Electrician for Residential Projects", "Landlord Wanted for High-Rise Office Building", "Available Office Space for Lease in Central Business District", "Real Estate Agent Wanted for Busy Agency", "Highly Skilled Data Scientist Needed for Project", "Full-time Receptionist <PERSON><PERSON><PERSON> Available in Medical Office", "Newly Constructed Condo Units for Sale in Upscale Neighborhood", "Sales Associate Wanted for Boutique Clothing Store", "Job Opening for a Seasoned IT Project Manager", "Well-Equipped Fitness Studio for Rent in Local Gym", "Highway Construction Worker Wanted for Ongoing Project", "Customer Success Manager Needed for Cloud-Based Software", "Looking for a Trustworthy Housekeeper for Private Residence", "Creative Graphic Designer Wanted for Marketing Agency", "Restaurant Manager Wanted for <PERSON><PERSON>er in Tourist Area", "Security Guard Needed for Local Office Complex", "Marketing Coordinator Position Open in Tech Startup"], "description": "The name of a job or property listing, including its title and description."}, {"input": "membership role", "examples": ["Admin", "Broker", "Buyer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Guest", "Premium Buyer", "Basic Seller", "Wholesale Buyer", "Real Estate Agent", "Interior Designer", "Builder", "Investor", "Reseller", "Exporter", "Importer", "Trader", "Manufacturer", "Distributor", "Premium Seller"], "description": "Identifies the role of a user within the platform, with possible values such as 'Admin', 'Broker', 'Buyer', 'Seller', 'Bidder', and 'Guest'."}, {"input": "organization id", "examples": ["ORG-12345", "AUCTION-HOUSE-1", "REALTY-123", "REMAX-NEW-YORK", "SO-the-AUCTION-HOUSE-123", "Auction-House-456", "REALTY-ABC", "ORG-67890", "123-AUCTION-STREET", "REALTY-XYZ-123", "123-NEW-YORK-ORG", "ORG-ORG-123", "THE-AUCTION-HOUSE-123", "REALTY-HOUSE-456", "Auction-123-ORG", "ORG-NEW-YORK-123", "REALTY-AUCTION-123", "THE-REMAX-123", "123-REALTY-STREET", "ORG-HOUSE-123"], "description": "Unique identifier for a user organization in the real estate or auction management platform."}, {"input": "organization name", "examples": ["Smithson Properties", "New York Auction House", "ABC Construction Inc.", "Green Earth Developers", "The Real Estate Group", "Cityscape Architects", "Pinnacle Builders LLC", "Urban Renewal Inc.", "Bayshore Properties", "Capital Investment Group", "Suburban Homes Inc.", "Premier Estates", "East Coast Development Corp.", "Infinity Builders Group", "Fortune Realty Inc.", "Gateway Construction Co.", "Southern Homes LLC", "Western Development Partners", "Pinnacle Development Corp.", "Tradition Builders Inc."], "description": "The name of the organization or entity, possibly a property developer, auction house, or contractor, associated with a particular record"}, {"input": "property id", "examples": ["P001", "P123", "P456", "P789", "P012", "P345", "P678", "P901", "P234", "P567", "P890", "P1234", "P5678", "P9012", "P3456", "P7890", "P12345", "P67890", "P90123", "P23456"], "description": "Unique identifier for a property listing in the system."}, {"input": "property name", "examples": ["Single Family Home at 123 Main St, Anytown USA", "3 Bedroom Apartment in Downtown Metropolis", "Vacation Rental: Studio Condo in Beachfront Complex", "Luxury Townhouse with private pool at 456 Oak St", "Commercial Office Space at 789 Broadway Ave", "Ranch Style Property with 3 car garage at 101 Farm Lane", "Duplex Unit for Rent at 234 Elm St", "Modern Loft Condo in City Center", "Farmland Property with barn and pasture at 567 Rural Rd", "High-Rise Condominium with ocean views at 891 Beach Dr", "Residential Lot for Sale at 345 Park Ave", "Historic Home Restoration Project at 678 Heritage St", "Warehouse Space for Lease at 901 Industrial Dr", "Single Family Home with mother-in-law suite at 1234 Maple St", "Co-op Apartment in Upper East Side, NYC", "Timber Frame Home on 10 acres at 5678 Forest Rd", "Mobile Home Community with amenities at 9012 Mobile Home Dr", "Condo with golf course views at 3456 Country Club Dr", "Twin Home with shared backyard at 7890 Oakwood St"], "description": "The name of a property listed in the system, potentially including details such as property type, address, or description."}, {"input": "session id", "examples": ["1", "214", "542", "119", "8", "6547", "1174", "20", "421", "1189", "67", "813", "4219", "219", "1", "4897", "56", "983", "179", "7481"], "description": "Uniquely identifies each session, probably utilizing an auto-incrementing integer."}, {"input": "session session_token", "examples": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.hK9b8hU8zW9Y6xXZ8ZdV9YdCJOwP8c8x9p5j2ZQz9jA", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.Vz8JbYzRqfT8w5Gvtsj6B1G8fU1J8eM1E1yU9D0jLsE", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.hK9b8hU8zW9Y6xXZ8ZdV9YdCJOwP8c8x9p5j2ZQz9jA", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.Vz8JbYzRqfT8w5Gvtsj6B1G8fU1J8eM1E1yU9D0jLsE", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.hK9b8hU8zW9Y6xXZ8ZdV9YdCJOwP8c8x9p5j2ZQz9jA", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.Vz8JbYzRqfT8w5Gvtsj6B1G8fU1J8eM1E1yU9D0jLsE", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.hK9b8hU8zW9Y6xXZ8ZdV9YdCJOwP8c8x9p5j2ZQz9jA", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJzdW1taW5pIn0.Vz8JbYzRqfT8w5Gvtsj6B1G8fU1J8eM1E1yU9D0jLsE", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"], "description": "A unique token generated for each user session, used to identify and authenticate the user."}, {"input": "task id", "examples": ["1", "238", "1029", "12345", "9999", "0", "5678", "123", "4567", "8901", "1001", "9987", "101", "999", "456", "7890", "6543", "2108", "5432", "9876"], "description": "Unique identifier for each task in the system, likely an auto-incrementing integer."}, {"input": "task name", "examples": ["Luxury home for sale in Beverly Hills", "Freelance web developer for hire", "Renovated apartment in Manhattan for rent", "Custom software development project", "New construction home in Austin TX", "Full-stack developer for part-time work", "Stunning beachfront property in Hawaii", "Interior designer for high-end interior projects", "Luxury yacht for charter in Mediterranean", "Professional freelance writer for content creation", "Modern townhouse in Brooklyn for sale", "E-commerce website development for small business", "<PERSON><PERSON>ury vacation home in the French Alps", "Experienced project manager for hire", "High-end interior design services for luxury homes", "New build home in Silicon Valley for sale", "Freelance social media marketer for small business", "Stunning penthouse apartment in New York City", "Custom mobile app development project", "Luxury vacation rental in Costa Rica"], "description": "The name of a task or property listing, such as a property for sale or a job to be bid on."}, {"input": "trade id", "examples": ["TL-001", "TR-101", "TS-1234", "TX-5678", "TY-9012", "TZ-3456", "T0-6789", "T1-2345", "T2-6789", "T3-4567", "T4-8901", "T5-2345", "T6-6789", "T7-4567", "T8-8901", "T9-2345", "TA-6789", "TB-4567", "TC-8901", "TD-2345"], "description": "Unique identifier for a trade listing in the system, likely a primary key."}, {"input": "trade name", "examples": ["Plumbing", "Electrical", "Construction", "Landscaping", "Interior Design", "Painting", "HVAC", "Roofing", "Flooring", "Concrete", "Gardening", "Pest Control"], "description": "Name of the trade, which could be a type of property, construction job, or auction lot."}, {"input": "user id", "examples": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "101", "102", "103", "104", "105", "1001", "1002", "1003", "12345", "67890"], "description": "Unique identifier assigned to each user in the database."}, {"input": "user name", "examples": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "MiaHall", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Elijah<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "AvaHall"], "description": "Unique name provided by the user during registration."}, {"input": "user email", "examples": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "description": "The email address of the registered user in the system"}, {"input": "user image", "examples": ["https://example.com/users/1/profile.jpg", "https://storage.googleapis.com/example-bucket/user-123-profile.png", "/user-images/uid-456.jpg", "https://s3.amazonaws.com/example-bucket/user-profile-image.jpg", "https://example.com/static/images/profile-picture.jpg", "/static/images/user-profile-image.png", "https://cdn.example.com/user-123-profile-image.jpg", "/user-profile-images/uid-678.jpg", "https://example.com/images/user-profile-image.jpg", "https://storage.yandexcloud.net/example-bucket/user-profile-image.png", "/images/user-profile-picture.jpg", "https://example.com/static/profile-picture.jpg", "https://example.com/user-profile-images/uid-901.png", "https://s3.amazonaws.com/example-bucket/user-profile-image.jpg", "/static/user-profile-image.jpg", "https://cdn.example.com/user-profile-image.jpg", "/images/user-profile-image.jpg", "https://example.com/user-123-profile-image.png", "https://example.com/user-profile-image.jpg", "https://storage.googleapis.com/example-bucket/user-profile-image.png", "https://s3.amazonaws.com/example-bucket/user-profile-image.jpg"], "description": "A URL or path to a user's profile image, likely stored on a server or a cloud storage service."}, {"input": "verification_token identifier", "examples": ["VT-**********", "ABCDEF012345", "**********ABC", "DEF123456789", "**********GHI", "ABCDEFGH1234", "IJKLMNOPQR123", "STUVWXYZ123456", "4567890JKLMNPQ", "RSTUVWXYZ12345", "ABCD123456789", "EFGH123456789", "IJKL123456789", "MNOPQ12345678", "MNOPQ123456789", "QRSTU12345678", "VWXYZ123456789", "**********WXYZ", "0987654321STU", "7654321098MNO"], "description": "A unique identifier used for verification purposes, likely generated upon user registration or other significant events within the platform."}, {"input": "verification_token token", "examples": ["9b06a8d6-5d2b-4c4a-8d1c-0b42a7ec5b7c", "f7a8d06b-2b5d-4c4a-8d1c-2b42a7ec5b7d", "c7a8d06b-5d2b-4c4a-8d1c-6b42a7ec5b7e", "5d6a8d06-2b5d-4c4a-8d1c-3b42a7ec5b7f", "a8d06b5d-5d2b-4c4a-8d1c-4b42a7ec5b80", "8d06b5d-2b5d-4c4a-8d1c-5b42a7ec5b81", "d06b5d2b-5d2b-4c4a-8d1c-6b42a7ec5b82", "06b5d2b-2b5d-4c4a-8d1c-7b42a7ec5b83", "b5d2b2b-5d2b-4c4a-8d1c-8b42a7ec5b84", "5d2b2b2b-5d2b-4c4a-8d1c-9b42a7ec5b85", "2b2b2b2b-5d2b-4c4a-8d1c-0b42a7ec5b86", "2b2b2b2b-2b5d-4c4a-8d1c-1b42a7ec5b87", "2b2b2b2b-2b5d-4c4a-8d1c-2b42a7ec5b88", "2b2b2b2b-2b5d-4c4a-8d1c-3b42a7ec5b89", "2b2b2b2b-2b5d-4c4a-8d1c-4b42a7ec5b8a", "2b2b2b2b-2b5d-4c4a-8d1c-5b42a7ec5b8b", "2b2b2b2b-2b5d-4c4a-8d1c-6b42a7ec5b8c", "2b2b2b2b-2b5d-4c4a-8d1c-7b42a7ec5b8d", "2b2b2b2b-2b5d-4c4a-8d1c-8b42a7ec5b8e", "2b2b2b2b-2b5d-4c4a-8d1c-9b42a7ec5b8f"], "description": "Unique verification token for user account verification or reset."}]