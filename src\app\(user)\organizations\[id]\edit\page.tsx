import { Header } from "@/components/header";
import { OrganizationForm } from "@/components/organization/organization-form";
import { getQueryClient, trpc } from "@/components/trpc/server";

export default async function EditOrganizationPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const queryClient = getQueryClient();
  const organization = await queryClient.fetchQuery(
    trpc.organizations.one.queryOptions({ id })
  );

  return (
    <>
      <Header title="Edit Organization" />
      <div className="p-8">
        <OrganizationForm initialData={organization} />
      </div>
    </>
  );
}
