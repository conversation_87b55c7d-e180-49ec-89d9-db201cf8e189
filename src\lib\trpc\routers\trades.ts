import "server-only";

import { z } from "zod";
import { db } from "@/db";
import { publicProcedure, router } from "../trpc";

export const tradesRouter = router({
  list: publicProcedure.query(async () => {
    const trades = await db.trade.findMany({
      orderBy: { name: "asc" },
      include: {
        organizations: true,
        _count: {
          select: { organizations: true },
        },
      },
    });
    return trades;
  }),
  create: publicProcedure
    .input(z.object({ name: z.string() }))
    .mutation(async ({ input }) => {
      const trade = await db.trade.create({
        data: {
          name: input.name,
        },
      });
      return trade;
    }),
  one: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const trade = await db.trade.findUnique({
        where: { id: input.id },
      });
      return trade;
    }),
  update: publicProcedure
    .input(z.object({ id: z.string(), name: z.string() }))
    .mutation(async ({ input }) => {
      const trade = await db.trade.update({
        where: { id: input.id },
        data: {
          name: input.name,
        },
      });
      return trade;
    }),
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      const trade = await db.trade.delete({
        where: { id: input.id },
      });
      return trade;
    }),
});
