"use client";

import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  CalendarIcon,
  ChevronDownIcon,
  ClockIcon,
  DollarSignIcon,
  HandIcon,
  PencilIcon,
  SendIcon,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { Header } from "@/components/header";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Prisma } from "@/db/generated";
import { getBidStatusVariant, getJobStatusVariant } from "@/lib/utils";

export default function JobDetailClient({
  job,
  userType,
}: {
  job: Prisma.JobGetPayload<{
    include: { property: true; tasks: true; bids: true; images: true };
  }>;
  userType: string;
}) {
  const router = useRouter();
  const trpc = useTRPC();
  const [isPublishing, setIsPublishing] = useState(false);

  const isProfessional = userType === "professional";
  const isDraft = job.status === "DRAFT";
  const isPublished = job.status === "PUBLISHED";

  const publishJob = useMutation(
    trpc.jobs.publish.mutationOptions({
      onSuccess: () => {
        toast.success("Project published successfully");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error publishing project: ${error.message}`);
      },
      onSettled: () => {
        setIsPublishing(false);
      },
    })
  );

  const handlePublish = () => {
    setIsPublishing(true);
    publishJob.mutate({ id: job.id });
  };

  return (
    <>
      <Header title={job.name}>
        <div className="flex gap-2">
          {!isProfessional && isDraft && (
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handlePublish}
              disabled={isPublishing}
            >
              <SendIcon className="mr-2 h-4 w-4" />
              {isPublishing ? "Publishing..." : "Publish Project"}
            </Button>
          )}

          {isProfessional && isPublished && (
            <Button className="bg-blue-600 hover:bg-blue-700" asChild>
              <Link href={`/jobs/${job.id}/bid`}>
                <HandIcon className="mr-2 h-4 w-4" />
                Bid Now
              </Link>
            </Button>
          )}

          {!isProfessional && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-1">
                  <PencilIcon className="h-4 w-4" />
                  Actions
                  <ChevronDownIcon className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/jobs/${job.id}/edit`}>Edit Project</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/jobs/${job.id}/tasks/new`}>Add Task</Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          <Button className="bg-orange-600 hover:bg-orange-700" asChild>
            <Link href="/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </Header>
      <div className="p-8">
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Project Details</CardTitle>
                  <Badge variant={getJobStatusVariant(job.status)}>
                    {job.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium">Property</h3>
                    <p>{job.property.name}</p>
                  </div>

                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Start Date
                        </p>
                        <p>{format(new Date(job.startsAt), "PPP")}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <ClockIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Deadline
                        </p>
                        <p>{format(new Date(job.deadline), "PPP")}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <DollarSignIcon className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-muted-foreground text-sm">Budget</p>
                        <p>${job.budget}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {job.images && job.images.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Project Images</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
                    {job.images.map((image, index) => (
                      <div
                        key={image.id}
                        className="overflow-hidden rounded-md border"
                      >
                        <div className="relative aspect-video">
                          <Image
                            src={image.url}
                            alt={
                              image.description || `Project image ${index + 1}`
                            }
                            fill
                            className="object-cover"
                          />
                        </div>
                        {image.description && (
                          <div className="p-3">
                            <p className="text-sm">{image.description}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Tasks</CardTitle>
              </CardHeader>
              <CardContent>
                {job.tasks.length === 0 ? (
                  <p className="text-muted-foreground">
                    No tasks added to this project.
                  </p>
                ) : (
                  <div className="space-y-4">
                    {job.tasks.map((task, index) => (
                      <div key={task.id} className="rounded-md border p-4">
                        <h4 className="font-medium">
                          Task {index + 1}: {task.name}
                        </h4>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Bids</CardTitle>
              </CardHeader>
              <CardContent>
                {job.bids && job.bids.length > 0 ? (
                  <div className="space-y-4">
                    {job.bids.map((bid) => (
                      <div key={bid.id} className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{bid.name}</h4>
                          <Badge variant={getBidStatusVariant(bid.status)}>
                            {bid.status}
                          </Badge>
                        </div>
                        <div className="mt-2 flex flex-wrap items-center gap-x-4 gap-y-1 text-sm">
                          <p className="text-muted-foreground">
                            Amount: ${bid.amount}
                          </p>
                          <p className="text-muted-foreground">
                            Duration: {bid.estimatedDuration} days
                          </p>
                        </div>
                        <div className="mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            asChild
                          >
                            <Link href={`/bids/${bid.id}`}>View Details</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">No bids received yet.</p>
                )}
              </CardContent>
            </Card>

            <div className="mt-6 space-y-4">
              {isDraft && !isProfessional && (
                <Button
                  className="w-full bg-green-600 hover:bg-green-700"
                  onClick={handlePublish}
                  disabled={isPublishing}
                >
                  <SendIcon className="mr-2 h-4 w-4" />
                  {isPublishing ? "Publishing..." : "Publish Project"}
                </Button>
              )}

              {!isProfessional && (
                <Button
                  className="w-full bg-orange-600 hover:bg-orange-700"
                  asChild
                >
                  <Link href={`/jobs/${job.id}/edit`}>Edit Project</Link>
                </Button>
              )}

              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard">Back to Dashboard</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
