import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { db } from "@/db";
import { protectedProcedure, router } from "../trpc";

export const bidsRouter = router({
  listForOrganization: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const bids = await db.bid.findMany({
        where: {
          organizationId: input.organizationId,
        },
        include: {
          job: {
            include: {
              property: {
                include: {
                  organization: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return bids;
    }),

  create: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        organizationId: z.string(),
        name: z.string(),
        amount: z.number(),
        description: z.string(),
        estimatedDuration: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if the user is part of the organization
      const membership = await db.membership.findFirst({
        where: {
          organizationId: input.organizationId,
          userId: ctx.auth.userId,
        },
      });

      if (!membership) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You are not a member of this organization",
        });
      }

      // Check if the job exists and is published
      const job = await db.job.findUnique({
        where: { id: input.jobId },
      });

      if (!job) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      if (job.status !== "PUBLISHED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot bid on a job that is not published",
        });
      }

      // Check if the organization has already bid on this job
      const existingBid = await db.bid.findFirst({
        where: {
          jobId: input.jobId,
          organizationId: input.organizationId,
        },
      });

      if (existingBid) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Your organization has already submitted a bid for this job",
        });
      }

      // Create the bid
      const bid = await db.bid.create({
        data: {
          name: input.name,
          jobId: input.jobId,
          organizationId: input.organizationId,
          amount: input.amount,
          description: input.description,
          estimatedDuration: input.estimatedDuration,
        },
      });

      return bid;
    }),

  one: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      const bid = await db.bid.findUnique({
        where: { id: input.id },
        include: {
          job: {
            include: {
              property: true,
            },
          },
          organization: {
            include: {
              trade: true,
              address: true,
            },
          },
        },
      });

      if (!bid) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Bid not found",
        });
      }

      // Check if user has permission to view this bid
      // Either they own the property or they submitted the bid
      const isPropertyOwner = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organization: {
            properties: {
              some: {
                jobs: {
                  some: {
                    id: bid.jobId,
                  },
                },
              },
            },
          },
        },
      });

      const isBidder = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organizationId: bid.organizationId,
        },
      });

      if (!isPropertyOwner && !isBidder) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You don't have permission to view this bid",
        });
      }

      return bid;
    }),

  accept: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      const bid = await db.bid.findUnique({
        where: { id: input.id },
        include: {
          job: {
            include: {
              property: {
                include: {
                  organization: true,
                },
              },
            },
          },
        },
      });

      if (!bid) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Bid not found",
        });
      }

      // Check if user has permission to accept this bid
      const isPropertyOwner = await db.membership.findFirst({
        where: {
          userId: ctx.auth.userId,
          organizationId: bid.job.property.organizationId,
        },
      });

      if (!isPropertyOwner) {
        throw new TRPCError({
          code: "UNAUTHORIZED",
          message: "You don't have permission to accept this bid",
        });
      }

      // Update the bid status to ACCEPTED
      const updatedBid = await db.bid.update({
        where: { id: input.id },
        data: {
          status: "ACCEPTED",
        },
      });

      // Update the job status to AWARDED
      await db.job.update({
        where: { id: bid.jobId },
        data: {
          status: "AWARDED",
        },
      });

      // Reject all other bids for this job
      await db.bid.updateMany({
        where: {
          jobId: bid.jobId,
          id: { not: input.id },
        },
        data: {
          status: "REJECTED",
        },
      });

      return updatedBid;
    }),
});
